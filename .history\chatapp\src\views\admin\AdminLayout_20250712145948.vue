<template>
  <n-config-provider :theme="darkTheme">
    <div class="admin-layout">
      <n-layout has-sider class="admin-main-layout">
        <!-- Modern Sidebar -->
        <n-layout-sider
          bordered
          collapse-mode="width"
          :collapsed-width="80"
          :width="280"
          :collapsed="collapsed"
          show-trigger
          @collapse="collapsed = true"
          @expand="collapsed = false"
          class="admin-sidebar"
        >
          <div class="sidebar-content">
            <!-- Logo Section -->
            <div class="sidebar-logo" :class="{ collapsed: collapsed }">
              <div class="logo-container">
                <div class="logo-icon">
                  <n-icon size="32" color="#18a058">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </n-icon>
                </div>
                <transition name="logo-text">
                  <div v-if="!collapsed" class="logo-text">
                    <h2>ROOM CHAT</h2>
                    <span>Admin Panel</span>
                  </div>
                </transition>
              </div>
            </div>

            <!-- Navigation Menu -->
            <div class="sidebar-menu">
              <n-menu
                :collapsed="collapsed"
                :collapsed-width="80"
                :collapsed-icon-size="22"
                :options="menuOptions"
                :value="activeKey"
                @update:value="handleMenuSelect"
                :indent="24"
                class="custom-menu"
              />
            </div>

            <!-- User Info Section -->
            <div class="sidebar-footer" :class="{ collapsed: collapsed }">
              <div class="user-info">
                <n-avatar
                  round
                  :size="collapsed ? 'medium' : 'large'"
                  :src="authStore.user?.avatar || '/default-avatar.png'"
                  class="user-avatar"
                />
                <transition name="user-text">
                  <div v-if="!collapsed" class="user-details">
                    <div class="user-name">{{ authStore.user?.username }}</div>
                    <div class="user-role">Quản trị viên</div>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </n-layout-sider>

        <!-- Main Content Area -->
        <n-layout class="main-content">
          <!-- Modern Header -->
          <n-layout-header class="admin-header">
            <div class="header-content">
              <div class="header-left">
                <div class="page-title">
                  <n-icon size="24" class="page-icon">
                    <component :is="currentPageIcon" />
                  </n-icon>
                  <h1>{{ currentPageTitle }}</h1>
                </div>
                <div class="breadcrumb">
                  <n-breadcrumb>
                    <n-breadcrumb-item>Admin</n-breadcrumb-item>
                    <n-breadcrumb-item>{{ currentPageTitle }}</n-breadcrumb-item>
                  </n-breadcrumb>
                </div>
              </div>

              <div class="header-right">
                <n-space size="large">
                  <!-- Notifications -->
                  <n-badge :value="3" :max="99">
                    <n-button quaternary circle size="large">
                      <n-icon size="20">
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                        </svg>
                      </n-icon>
                    </n-button>
                  </n-badge>

                  <!-- Back to Chat -->
                  <n-button @click="$router.push('/chat')" type="primary" ghost>
                    <template #icon>
                      <n-icon>
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M12 3c5.5 0 10 3.58 10 8s-4.5 8-10 8c-1.24 0-2.43-.18-3.53-.5C5.55 21 2 21 2 21c2.33-2.33 2.7-3.9 2.75-4.5C3.05 15.07 2 13.13 2 11c0-4.42 4.5-8 10-8Z"/>
                        </svg>
                      </n-icon>
                    </template>
                    Về Chat
                  </n-button>

                  <!-- User Menu -->
                  <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect" trigger="click">
                    <n-button quaternary circle size="large" class="user-menu-btn">
                      <n-avatar
                        round
                        size="medium"
                        :src="authStore.user?.avatar || '/default-avatar.png'"
                      />
                    </n-button>
                  </n-dropdown>
                </n-space>
              </div>
            </div>
          </n-layout-header>

          <!-- Content Area -->
          <n-layout-content class="admin-content">
            <div class="content-wrapper">
              <router-view />
            </div>
          </n-layout-content>
        </n-layout>
      </n-layout>
    </div>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { darkTheme, NIcon } from 'naive-ui'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const collapsed = ref(false)

const activeKey = computed(() => route.name)

const currentPageTitle = computed(() => {
  const titles = {
    'AdminDashboard': 'Tổng quan',
    'AdminUsers': 'Quản lý người dùng',
    'AdminMessages': 'Quản lý tin nhắn',
    'AdminBlacklist': 'Danh sách đen'
  }
  return titles[route.name] || 'Trang chủ'
})

const currentPageIcon = computed(() => {
  const icons = {
    'AdminDashboard': h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z' })
    ]),
    'AdminUsers': h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4' })
    ]),
    'AdminMessages': h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4C22,2.89 21.1,2 20,2Z' })
    ]),
    'AdminBlacklist': h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z' })
    ])
  }
  return icons[route.name] || icons['AdminDashboard']
})

const renderIcon = (icon) => {
  return () => h(NIcon, null, { default: () => h('div', { innerHTML: icon }) })
}

const menuOptions = [
  {
    label: 'Tổng quan',
    key: 'AdminDashboard',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/></svg>`)
  },
  {
    label: 'Người dùng',
    key: 'AdminUsers',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4M10.5 12.5c.28 0 .5.22.5.5s-.22.5-.5.5s-.5-.22-.5-.5s.22-.5.5-.5M15 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1M12.5 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1M10 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1"/></svg>`)
  },
  {
    label: 'Tin nhắn',
    key: 'AdminMessages',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/></svg>`)
  },
  {
    label: 'Danh sách đen',
    key: 'AdminBlacklist',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/></svg>`)
  }
]

const userMenuOptions = [
  {
    label: 'Hồ sơ',
    key: 'profile',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>`)
  },
  {
    label: 'Đăng xuất',
    key: 'logout',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 17v-3H9v-4h7V7l5 5l-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H5v16h9v-2h2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9Z"/></svg>`)
  }
]

const handleMenuSelect = (key) => {
  router.push({ name: key })
}

const handleUserMenuSelect = (key) => {
  if (key === 'logout') {
    authStore.logout()
    router.push('/login')
  } else if (key === 'profile') {
    router.push('/profile')
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  background: #001529;
}

.admin-sidebar {
  height: 100%;
  background: #001529;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #303030;
  margin-bottom: 16px;
}

.admin-header {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-actions {
  display: flex;
  align-items: center;
}
</style>
