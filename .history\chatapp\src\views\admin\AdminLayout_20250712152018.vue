<template>
  <n-config-provider :theme="darkTheme" :theme-overrides="themeOverrides">
    <div class="admin-layout">
      <n-layout has-sider class="admin-main-layout">
        <!-- Modern Sidebar -->
        <n-layout-sider
          :collapsed="collapsed"
          :collapsed-width="80"
          :width="280"
          collapse-mode="width"
          show-trigger="bar"
          trigger-style="top: 12px; background: rgba(255, 255, 255, 0.1); border: none; border-radius: 8px;"
          @collapse="collapsed = true"
          @expand="collapsed = false"
          class="admin-sidebar"
        >
          <div class="sidebar-content">
            <!-- Logo Section -->
            <div class="sidebar-logo" :class="{ collapsed: collapsed }">
              <div class="logo-container">
                <div class="logo-icon">
                  <n-icon size="32" color="#00d4aa">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </n-icon>
                </div>
                <transition name="logo-text">
                  <div v-if="!collapsed" class="logo-text">
                    <h2>ROOM CHAT</h2>
                    <span>Admin Panel</span>
                  </div>
                </transition>
              </div>
            </div>

            <!-- Navigation Menu -->
            <div class="sidebar-menu">
              <n-menu
                :collapsed="collapsed"
                :collapsed-width="80"
                :collapsed-icon-size="22"
                :options="menuOptions"
                :value="activeKey"
                @update:value="handleMenuSelect"
                :root-indent="24"
                :indent="32"
                class="custom-menu"
              />
            </div>

            <!-- User Info at Bottom -->
            <div class="sidebar-footer" :class="{ collapsed: collapsed }">
              <div class="user-info">
                <n-avatar
                  round
                  :size="collapsed ? 40 : 48"
                  :src="authStore.user?.avatar || '/default-avatar.png'"
                  class="user-avatar"
                />
                <transition name="user-text">
                  <div v-if="!collapsed" class="user-details">
                    <div class="user-name">{{ authStore.user?.username || 'Admin' }}</div>
                    <div class="user-role">Quản trị viên</div>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </n-layout-sider>

        <!-- Main Content Area -->
        <n-layout class="main-content-layout">
          <!-- Modern Header -->
          <n-layout-header class="admin-header">
            <div class="header-content">
              <div class="header-left">
                <div class="page-title-section">
                  <h1 class="page-title">{{ currentPageTitle }}</h1>
                  <n-breadcrumb class="page-breadcrumb">
                    <n-breadcrumb-item>
                      <n-icon class="breadcrumb-icon">
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                      </n-icon>
                      Admin
                    </n-breadcrumb-item>
                    <n-breadcrumb-item>{{ currentPageTitle }}</n-breadcrumb-item>
                  </n-breadcrumb>
                </div>
              </div>

              <div class="header-right">
                <n-space :size="16">
                  <!-- Notifications -->
                  <n-badge :value="3" :max="99" show-zero>
                    <n-button quaternary circle size="large" class="header-action-btn">
                      <n-icon size="20">
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                        </svg>
                      </n-icon>
                    </n-button>
                  </n-badge>

                  <!-- Back to Chat -->
                  <n-button @click="$router.push('/chat')" type="primary" ghost class="back-to-chat-btn">
                    <template #icon>
                      <n-icon>
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                      </n-icon>
                    </template>
                    Về Chat
                  </n-button>

                  <!-- User Menu -->
                  <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect" trigger="click">
                    <n-button quaternary circle size="large" class="user-menu-btn">
                      <n-avatar
                        round
                        size="medium"
                        :src="authStore.user?.avatar || '/default-avatar.png'"
                      />
                    </n-button>
                  </n-dropdown>
                </n-space>
              </div>
            </div>
          </n-layout-header>

          <!-- Content Area with Smooth Transitions -->
          <n-layout-content class="admin-content">
            <div class="content-wrapper">
              <transition name="page-transition" mode="out-in">
                <router-view />
              </transition>
            </div>
          </n-layout-content>
        </n-layout>
      </n-layout>
    </div>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { darkTheme, NIcon } from 'naive-ui'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const collapsed = ref(false)

const activeKey = computed(() => route.name)

const currentPageTitle = computed(() => {
  const titles = {
    'AdminDashboard': 'Tổng quan',
    'AdminUsers': 'Quản lý người dùng',
    'AdminMessages': 'Quản lý tin nhắn',
    'AdminBlacklist': 'Danh sách đen'
  }
  return titles[route.name] || 'Trang chủ'
})

// Enhanced theme overrides for modern look
const themeOverrides = {
  common: {
    primaryColor: '#00d4aa',
    primaryColorHover: '#00e6c0',
    primaryColorPressed: '#00b894',
    borderRadius: '12px',
    borderRadiusSmall: '8px'
  },
  Layout: {
    siderColor: 'rgba(16, 16, 20, 0.95)',
    headerColor: 'rgba(24, 24, 28, 0.95)',
    color: 'rgba(16, 16, 20, 1)'
  },
  Menu: {
    itemColorActive: 'rgba(0, 212, 170, 0.15)',
    itemColorActiveHover: 'rgba(0, 212, 170, 0.25)',
    itemTextColorActive: '#00d4aa',
    itemIconColorActive: '#00d4aa',
    itemColorHover: 'rgba(255, 255, 255, 0.08)',
    itemTextColorHover: '#ffffff',
    itemIconColorHover: '#ffffff',
    itemTextColor: 'rgba(255, 255, 255, 0.75)',
    itemIconColor: 'rgba(255, 255, 255, 0.75)',
    borderRadius: '10px'
  },
  Button: {
    borderRadiusMedium: '10px',
    borderRadiusLarge: '12px'
  },
  Card: {
    borderRadius: '16px'
  }
}

const renderIcon = (icon) => {
  return () => h(NIcon, null, { default: () => h('div', { innerHTML: icon }) })
}

const menuOptions = [
  {
    label: 'Tổng quan',
    key: 'AdminDashboard',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>`)
  },
  {
    label: 'Người dùng',
    key: 'AdminUsers',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/></svg>`)
  },
  {
    label: 'Tin nhắn',
    key: 'AdminMessages',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg>`)
  },
  {
    label: 'Danh sách đen',
    key: 'AdminBlacklist',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>`)
  }
]

const userMenuOptions = [
  {
    label: 'Hồ sơ',
    key: 'profile',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>`)
  },
  {
    label: 'Đăng xuất',
    key: 'logout',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 17v-3H9v-4h7V7l5 5l-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H5v16h9v-2h2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9Z"/></svg>`)
  }
]

const handleMenuSelect = (key) => {
  router.push({ name: key })
}

const handleUserMenuSelect = (key) => {
  if (key === 'logout') {
    authStore.logout()
    router.push('/login')
  } else if (key === 'profile') {
    router.push('/profile')
  }
}
</script>

<style scoped>
/* Main Layout */
.admin-layout {
  height: 100vh;
  background: linear-gradient(135deg, #0c0c0f 0%, #161620 100%);
  overflow: hidden;
}

.admin-main-layout {
  height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
  background: rgba(16, 16, 20, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* Logo Section */
.sidebar-logo {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-logo.collapsed {
  padding: 24px 16px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
  transition: all 0.3s ease;
}

.logo-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
}

.logo-text h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.logo-text span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Menu Section */
.sidebar-menu {
  flex: 1;
  padding: 16px 12px;
  overflow-y: auto;
}

.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

/* Custom Menu Styles */
:deep(.custom-menu .n-menu-item) {
  margin: 4px 0;
  border-radius: 10px;
  transition: all 0.3s ease;
}

:deep(.custom-menu .n-menu-item:hover) {
  transform: translateX(4px);
}

:deep(.custom-menu .n-menu-item-content) {
  padding: 12px 16px;
  border-radius: 10px;
}

:deep(.custom-menu .n-menu-item-content-header) {
  font-weight: 500;
  font-size: 14px;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.sidebar-footer.collapsed {
  padding: 20px 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.05);
}

.user-avatar {
  border: 2px solid rgba(0, 212, 170, 0.3);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  border-color: #00d4aa;
  box-shadow: 0 0 12px rgba(0, 212, 170, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Header Styles */
.admin-header {
  height: 80px;
  background: rgba(24, 24, 28, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  height: 100%;
  padding: 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.page-breadcrumb {
  opacity: 0.7;
}

:deep(.page-breadcrumb .n-breadcrumb-item) {
  font-size: 13px;
}

.breadcrumb-icon {
  margin-right: 4px;
}

/* Header Actions */
.header-right {
  display: flex;
  align-items: center;
}

.header-action-btn {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.header-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.back-to-chat-btn {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-to-chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

.user-menu-btn {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.user-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Content Area */
.admin-content {
  background: linear-gradient(135deg, #0f0f13 0%, #1a1a24 100%);
  min-height: calc(100vh - 80px);
}

.content-wrapper {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Animations */
.logo-text-enter-active,
.logo-text-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-text-enter-from,
.logo-text-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.user-text-enter-active,
.user-text-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-text-enter-from,
.user-text-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
