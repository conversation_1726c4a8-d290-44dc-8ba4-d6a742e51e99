<template>
  <n-config-provider :theme="darkTheme">
    <div class="admin-layout">
      <n-layout has-sider>
        <n-layout-sider
          bordered
          collapse-mode="width"
          :collapsed-width="64"
          :width="240"
          :collapsed="collapsed"
          show-trigger
          @collapse="collapsed = true"
          @expand="collapsed = false"
        >
          <div class="admin-sidebar">
            <div class="logo">
              <n-text strong style="font-size: 18px; color: white;">
                {{ collapsed ? 'TC' : 'TUNGDUONGCMS' }}
              </n-text>
            </div>
            
            <n-menu
              :collapsed="collapsed"
              :collapsed-width="64"
              :collapsed-icon-size="20"
              :options="menuOptions"
              :value="activeKey"
              @update:value="handleMenuSelect"
            />
          </div>
        </n-layout-sider>
        
        <n-layout>
          <n-layout-header bordered style="height: 64px; padding: 0 24px;">
            <div class="admin-header">
              <n-breadcrumb>
                <n-breadcrumb-item>Quản trị</n-breadcrumb-item>
                <n-breadcrumb-item>{{ currentPageTitle }}</n-breadcrumb-item>
              </n-breadcrumb>
              
              <div class="header-actions">
                <n-space>
                  <n-button @click="$router.push('/chat')" quaternary>
                    <template #icon>
                      <n-icon>
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M12 3c5.5 0 10 3.58 10 8s-4.5 8-10 8c-1.24 0-2.43-.18-3.53-.5C5.55 21 2 21 2 21c2.33-2.33 2.7-3.9 2.75-4.5C3.05 15.07 2 13.13 2 11c0-4.42 4.5-8 10-8Z"/>
                        </svg>
                      </n-icon>
                    </template>
                    Về Chat
                  </n-button>
                  
                  <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
                    <n-button quaternary circle>
                      <n-avatar
                        round
                        size="small"
                        :src="authStore.user?.avatar || '/default-avatar.png'"
                      />
                    </n-button>
                  </n-dropdown>
                </n-space>
              </div>
            </div>
          </n-layout-header>
          
          <n-layout-content style="padding: 24px;">
            <router-view />
          </n-layout-content>
        </n-layout>
      </n-layout>
    </div>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { darkTheme, NIcon } from 'naive-ui'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const collapsed = ref(false)

const activeKey = computed(() => route.name)

const currentPageTitle = computed(() => {
  const titles = {
    'AdminDashboard': 'Tổng quan',
    'AdminUsers': 'Quản lý người dùng',
    'AdminMessages': 'Quản lý tin nhắn',
    'AdminBlacklist': 'Danh sách đen'
  }
  return titles[route.name] || 'Trang chủ'
})

const renderIcon = (icon) => {
  return () => h(NIcon, null, { default: () => h('div', { innerHTML: icon }) })
}

const menuOptions = [
  {
    label: 'Tổng quan',
    key: 'AdminDashboard',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/></svg>`)
  },
  {
    label: 'Người dùng',
    key: 'AdminUsers',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4M10.5 12.5c.28 0 .5.22.5.5s-.22.5-.5.5s-.5-.22-.5-.5s.22-.5.5-.5M15 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1M12.5 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1M10 16c0 .55-.45 1-1 1s-1-.45-1-1s.45-1 1-1s1 .45 1 1"/></svg>`)
  },
  {
    label: 'Tin nhắn',
    key: 'AdminMessages',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/></svg>`)
  },
  {
    label: 'Danh sách đen',
    key: 'AdminBlacklist',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/></svg>`)
  }
]

const userMenuOptions = [
  {
    label: 'Hồ sơ',
    key: 'profile',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>`)
  },
  {
    label: 'Đăng xuất',
    key: 'logout',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 17v-3H9v-4h7V7l5 5l-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H5v16h9v-2h2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9Z"/></svg>`)
  }
]

const handleMenuSelect = (key) => {
  router.push({ name: key })
}

const handleUserMenuSelect = (key) => {
  if (key === 'logout') {
    authStore.logout()
    router.push('/login')
  } else if (key === 'profile') {
    router.push('/profile')
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  background: #001529;
}

.admin-sidebar {
  height: 100%;
  background: #001529;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #303030;
  margin-bottom: 16px;
}

.admin-header {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-actions {
  display: flex;
  align-items: center;
}
</style>
