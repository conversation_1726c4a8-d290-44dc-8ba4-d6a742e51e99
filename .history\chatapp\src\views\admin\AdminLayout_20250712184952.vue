<template>
  <n-config-provider :theme="darkTheme">
    <n-layout has-sider class="admin-layout">
      <n-layout-sider
        bordered
        collapse-mode="width"
        :collapsed-width="isMobile ? 0 : 64"
        :width="240"
        :collapsed="isMobile ? mobileCollapsed : collapsed"
        :show-trigger="!isMobile"
        :native-scrollbar="false"
        :inverted="true"
        class="admin-sider"
        @collapse="handleCollapse"
        @expand="handleExpand"
      >
        <div class="admin-sidebar">
          <div class="sidebar-header">
            <div class="logo">
              <n-text strong class="logo-text">
                {{ (isMobile ? mobileCollapsed : collapsed) ? 'TC' : 'TUNGDUONGCMS' }}
              </n-text>
            </div>
            <n-button
              v-if="isMobile"
              quaternary
              circle
              class="mobile-close-btn"
              @click="showMobileMenu = false"
            >
              <template #icon>
                <n-icon size="18">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41z"/>
                  </svg>
                </n-icon>
              </template>
            </n-button>
          </div>
          
          <div class="sidebar-content">
            <n-menu
              :collapsed="isMobile ? mobileCollapsed : collapsed"
              :collapsed-width="isMobile ? 0 : 64"
              :collapsed-icon-size="20"
              :options="menuOptions"
              :value="activeKey"
              :inverted="true"
              :root-indent="24"
              :indent="32"
              @update:value="handleMenuSelect"
            />
          </div>
        </div>
      </n-layout-sider>
      
      <n-layout class="admin-main">
        <n-layout-header 
          bordered 
          class="admin-header"
          :style="{ 
            height: '64px', 
            padding: isMobile ? '0 16px' : '0 24px',
            position: 'relative',
            zIndex: 1000
          }"
        >
          <div class="header-content">
            <div class="header-left">
              <n-button
                v-if="isMobile"
                quaternary
                circle
                class="mobile-menu-btn"
                @click="showMobileMenu = true"
              >
                <template #icon>
                  <n-icon size="18">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-button>
              
              <n-breadcrumb v-if="!isMobile" separator=">">
                <n-breadcrumb-item>
                  <n-icon size="16" style="margin-right: 4px;">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M10 20v-6h4v6h5v-8h3L12 3L2 12h3v8h5Z"/>
                    </svg>
                  </n-icon>
                  Quản trị
                </n-breadcrumb-item>
                <n-breadcrumb-item>{{ currentPageTitle }}</n-breadcrumb-item>
              </n-breadcrumb>
              
              <n-text v-else strong>{{ currentPageTitle }}</n-text>
            </div>
            
            <div class="header-right">
              <n-space :size="isMobile ? 8 : 16">
                <n-button 
                  @click="$router.push('/chat')" 
                  :quaternary="!isMobile"
                  :text="isMobile"
                  :size="isMobile ? 'small' : 'medium'"
                >
                  <template #icon>
                    <n-icon :size="isMobile ? 16 : 18">
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 3c5.5 0 10 3.58 10 8s-4.5 8-10 8c-1.24 0-2.43-.18-3.53-.5C5.55 21 2 21 2 21c2.33-2.33 2.7-3.9 2.75-4.5C3.05 15.07 2 13.13 2 11c0-4.42 4.5-8 10-8Z"/>
                      </svg>
                    </n-icon>
                  </template>
                  <span v-if="!isMobile">Về Chat</span>
                </n-button>
                
                <n-dropdown 
                  :options="userMenuOptions" 
                  @select="handleUserMenuSelect"
                  trigger="click"
                  :show-arrow="true"
                >
                  <n-button quaternary circle>
                    <n-avatar
                      round
                      :size="isMobile ? 32 : 36"
                      :src="authStore.user?.avatar || '/default-avatar.png'"
                      fallback-src="/default-avatar.png"
                    />
                  </n-button>
                </n-dropdown>
              </n-space>
            </div>
          </div>
        </n-layout-header>
        
        <n-layout-content 
          :style="{ 
            padding: isMobile ? '16px' : '24px',
            minHeight: 'calc(100vh - 64px)',
            backgroundColor: '#f5f5f5'
          }"
          :native-scrollbar="false"
        >
          <div class="content-wrapper">
            <router-view />
          </div>
        </n-layout-content>
      </n-layout>
    </n-layout>
    
    <n-modal
      v-if="isMobile"
      v-model:show="showMobileMenu"
      :mask-closable="true"
      :close-on-esc="true"
      :auto-focus="false"
      transform-origin="center"
      style="width: 280px; max-width: 90vw;"
    >
      <div class="mobile-sidebar-modal">
        <div class="mobile-sidebar-header">
          <n-text strong class="mobile-logo">TUNGDUONGCMS</n-text>
          <n-button
            quaternary
            circle
            @click="showMobileMenu = false"
          >
            <template #icon>
              <n-icon size="18">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41z"/>
                </svg>
              </n-icon>
            </template>
          </n-button>
        </div>
        
        <n-menu
          :options="menuOptions"
          :value="activeKey"
          :inverted="true"
          :root-indent="24"
          :indent="32"
          @update:value="handleMobileMenuSelect"
        />
      </div>
    </n-modal>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, h, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { darkTheme, NIcon, useMessage, useDialog } from 'naive-ui'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const message = useMessage()
const dialog = useDialog()

const collapsed = ref(false)
const mobileCollapsed = ref(true)
const showMobileMenu = ref(false)
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    mobileCollapsed.value = true
    showMobileMenu.value = false
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

const activeKey = computed(() => route.name)

const currentPageTitle = computed(() => {
  const titles = {
    'AdminDashboard': 'Tổng quan',
    'AdminUsers': 'Quản lý người dùng', 
    'AdminMessages': 'Quản lý tin nhắn',
    'AdminBlacklist': 'Danh sách đen',
    'AdminSettings': 'Cài đặt hệ thống',
    'AdminReports': 'Báo cáo thống kê'
  }
  return titles[route.name] || 'Trang chủ'
})

const renderIcon = (icon) => {
  return () => h(NIcon, { size: 18 }, { default: () => h('div', { innerHTML: icon }) })
}

const menuOptions = [
  {
    label: 'Tổng quan',
    key: 'AdminDashboard',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/></svg>`)
  },
  {
    label: 'Quản lý người dùng',
    key: 'AdminUsers',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4"/></svg>`)
  },
  {
    label: 'Quản lý tin nhắn',
    key: 'AdminMessages',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg>`)
  },
  {
    label: 'Danh sách đen',
    key: 'AdminBlacklist',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/></svg>`)
  },
  {
    label: 'Báo cáo thống kê',
    key: 'AdminReports',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>`)
  },
  {
    label: 'Cài đặt hệ thống',
    key: 'AdminSettings',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2z"/></svg>`)
  }
]

const userMenuOptions = [
  {
    label: 'Hồ sơ cá nhân',
    key: 'profile',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>`)
  },
  {
    label: 'Cài đặt tài khoản',
    key: 'settings',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/></svg>`)
  },
  {
    type: 'divider'
  },
  {
    label: 'Đăng xuất',
    key: 'logout',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 17v-3H9v-4h7V7l5 5l-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H5v16h9v-2h2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9Z"/></svg>`)
  }
]

const handleCollapse = () => {
  collapsed.value = true
}

const handleExpand = () => {
  collapsed.value = false
}

const handleMenuSelect = (key) => {
  try {
    router.push({ name: key })
  } catch (error) {
    message.error('Không thể chuyển đến trang này')
  }
}

const handleMobileMenuSelect = (key) => {
  showMobileMenu.value = false
  setTimeout(() => {
    handleMenuSelect(key)
  }, 200)
}

const handleUserMenuSelect = (key) => {
  switch (key) {
    case 'logout':
      dialog.warning({
        title: 'Xác nhận đăng xuất',
        content: 'Bạn có chắc chắn muốn đăng xuất khỏi hệ thống?',
        positiveText: 'Đăng xuất',
        negativeText: 'Hủy',
        onPositiveClick: () => {
          authStore.logout()
          router.push('/login')
          message.success('Đã đăng xuất thành công')
        }
      })
      break
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    default:
      break
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  background: #f5f5f5;
}

.admin-sider {
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #001529;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.logo-text {
  font-size: 18px;
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.mobile-close-btn {
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.7);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.admin-main {
  margin-left: 240px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
}

.mobile-menu-btn {
  margin-right: 8px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 128px);
}

.mobile-sidebar-modal {
  background: #001529;
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
}

.mobile-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-logo {
  color: white;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

@media (max-width: 767px) {
  .admin-main {
    margin-left: 0;
  }
  
  .admin-sider {
    transform: translateX(-100%);
  }
  
  .content-wrapper {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    min-height: calc(100vh - 96px);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .admin-main.collapsed {
    margin-left: 64px;
  }
  
  .content-wrapper {
    margin: 0 16px;
  }
}

@media (min-width: 1025px) {
  .admin-main.collapsed {
    margin-left: 64px;
  }
}

:deep(.n-layout-sider .n-layout-toggle-button) {
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.n-layout-sider .n-layout-toggle-button:hover) {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

:deep(.n-menu .n-menu-item) {
  margin: 4px 12px;
  border-radius: 8px;
}

:deep(.n-menu .n-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1);
}

:deep(.n-menu .n-menu-item.n-menu-item--selected) {
  background: rgba(24, 144, 255, 0.2);
  color: #1890ff;
}

:deep(.n-breadcrumb .n-breadcrumb-item .n-breadcrumb-item__link) {
  color: rgba(0, 0, 0, 0.65);
  font-weight: 500;
}

:deep(.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link) {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
}
</style>