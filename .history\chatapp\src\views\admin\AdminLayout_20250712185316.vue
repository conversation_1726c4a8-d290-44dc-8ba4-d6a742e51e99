<template>
  <n-config-provider :theme="darkTheme" :theme-overrides="themeOverrides">
    <div class="admin-layout">
      <n-layout has-sider class="layout-container">
        <n-layout-sider
          bordered
          collapse-mode="width"
          :collapsed-width="isMobile ? 0 : 64"
          :width="240"
          :collapsed="collapsed"
          :show-trigger="!isMobile"
          :native-scrollbar="false"
          :inverted="true"
          @collapse="handleCollapse"
          @expand="handleExpand"
          class="admin-sider"
        >
          <div class="admin-sidebar" :class="{ 'mobile-sidebar': isMobile }">
            <div class="logo-container">
              <div class="logo" @click="handleLogoClick">
                <div class="logo-icon">
                  <n-icon size="24" color="#64ffda">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                    </svg>
                  </n-icon>
                </div>
                <div class="logo-text" v-show="!collapsed || isMobile">
                  <n-text strong class="brand-name">TUNGDUONG</n-text>
                  <n-text class="brand-subtitle">CMS</n-text>
                </div>
              </div>
            </div>
            
            <n-scrollbar class="menu-scrollbar">
              <n-menu
                :collapsed="collapsed && !isMobile"
                :collapsed-width="64"
                :collapsed-icon-size="22"
                :options="menuOptions"
                :value="activeKey"
                :inverted="true"
                :accordion="true"
                @update:value="handleMenuSelect"
                class="admin-menu"
              />
            </n-scrollbar>
            
            <div class="sidebar-footer" v-show="!collapsed || isMobile">
              <n-divider style="margin: 16px 0; opacity: 0.1;" />
              <div class="user-info">
                <n-avatar
                  round
                  size="small"
                  :src="authStore.user?.avatar || '/default-avatar.png'"
                  fallback-src="/default-avatar.png"
                />
                <div class="user-details" v-show="!collapsed">
                  <n-text strong style="font-size: 14px;">{{ authStore.user?.name || 'Admin' }}</n-text>
                  <n-text depth="3" style="font-size: 12px;">{{ authStore.user?.email || '<EMAIL>' }}</n-text>
                </div>
              </div>
            </div>
          </div>
        </n-layout-sider>
        
        <n-layout class="main-layout">
          <n-layout-header bordered class="admin-header">
            <div class="header-container">
              <div class="header-left">
                <n-button 
                  v-if="isMobile"
                  quaternary 
                  circle 
                  @click="toggleSidebar"
                  class="mobile-menu-btn"
                >
                  <n-icon size="18">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2Z"/>
                    </svg>
                  </n-icon>
                </n-button>
                
                <n-breadcrumb class="breadcrumb">
                  <n-breadcrumb-item>
                    <n-icon size="16" style="margin-right: 4px;">
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/>
                      </svg>
                    </n-icon>
                    Quản trị
                  </n-breadcrumb-item>
                  <n-breadcrumb-item>{{ currentPageTitle }}</n-breadcrumb-item>
                </n-breadcrumb>
              </div>
              
              <div class="header-right">
                <n-space :size="12">
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-button 
                        @click="$router.push('/chat')" 
                        quaternary 
                        circle
                        class="header-btn"
                      >
                        <n-icon size="18">
                          <svg viewBox="0 0 24 24">
                            <path fill="currentColor" d="M12 3c5.5 0 10 3.58 10 8s-4.5 8-10 8c-1.24 0-2.43-.18-3.53-.5C5.55 21 2 21 2 21c2.33-2.33 2.7-3.9 2.75-4.5C3.05 15.07 2 13.13 2 11c0-4.42 4.5-8 10-8Z"/>
                          </svg>
                        </n-icon>
                      </n-button>
                    </template>
                    Về Chat
                  </n-tooltip>
                  
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-button quaternary circle class="header-btn">
                        <n-icon size="18">
                          <svg viewBox="0 0 24 24">
                            <path fill="currentColor" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                          </svg>
                        </n-icon>
                      </n-button>
                    </template>
                    Thông báo
                  </n-tooltip>
                  
                  <n-dropdown 
                    :options="userMenuOptions" 
                    @select="handleUserMenuSelect"
                    trigger="click"
                    placement="bottom-end"
                  >
                    <n-button quaternary circle class="user-avatar-btn">
                      <n-avatar
                        round
                        size="small"
                        :src="authStore.user?.avatar || '/default-avatar.png'"
                        fallback-src="/default-avatar.png"
                      />
                    </n-button>
                  </n-dropdown>
                </n-space>
              </div>
            </div>
          </n-layout-header>
          
          <n-layout-content class="admin-content">
            <div class="content-container">
              <router-view v-slot="{ Component }">
                <transition name="fade" mode="out-in">
                  <component :is="Component" />
                </transition>
              </router-view>
            </div>
          </n-layout-content>
        </n-layout>
      </n-layout>
      
      <div 
        v-if="isMobile && !collapsed" 
        class="mobile-overlay"
        @click="closeMobileSidebar"
      ></div>
    </div>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, h, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { darkTheme, NIcon } from 'naive-ui'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const collapsed = ref(false)
const isMobile = ref(false)

const themeOverrides = {
  common: {
    primaryColor: '#64ffda',
    primaryColorHover: '#4fd3b8',
    primaryColorPressed: '#26a69a',
    primaryColorSuppl: '#64ffda',
    infoColor: '#2196f3',
    successColor: '#4caf50',
    warningColor: '#ff9800',
    errorColor: '#f44336',
    textColor1: '#ffffff',
    textColor2: '#e0e0e0',
    textColor3: '#bdbdbd',
    textColorDisabled: '#757575',
    placeholderColor: '#9e9e9e',
    iconColor: '#e0e0e0',
    iconColorHover: '#64ffda',
    iconColorPressed: '#4fd3b8',
    iconColorDisabled: '#757575',
    opacity1: '0.82',
    opacity2: '0.72',
    opacity3: '0.38',
    opacity4: '0.24',
    opacity5: '0.18',
    dividerColor: '#424242',
    borderColor: '#424242',
    closeIconColor: '#bdbdbd',
    closeIconColorHover: '#64ffda',
    closeIconColorPressed: '#4fd3b8',
    clearColor: '#bdbdbd',
    clearColorHover: '#64ffda',
    clearColorPressed: '#4fd3b8',
    scrollbarColor: '#ffffff14',
    scrollbarColorHover: '#ffffff1f',
    scrollbarWidth: '6px',
    scrollbarHeight: '6px',
    scrollbarBorderRadius: '3px',
    progressRailColor: '#424242',
    railColor: '#424242',
    popoverColor: '#2c2c2c',
    tableColor: '#1a1a1a',
    cardColor: '#1e1e1e',
    modalColor: '#1a1a1a',
    bodyColor: '#121212',
    tagColor: '#1e1e1e',
    avatarColor: '#424242',
    invertedColor: '#0f0f0f',
    inputColor: '#1e1e1e',
    codeColor: '#1e1e1e',
    tabColor: '#1e1e1e',
    actionColor: '#1e1e1e',
    tableHeaderColor: '#1e1e1e',
    hoverColor: '#ffffff0a',
    tableColorHover: '#ffffff0a',
    tableColorStriped: '#ffffff05',
    pressedColor: '#ffffff0f',
    opacityDisabled: '0.38',
    inputColorDisabled: '#424242',
    buttonColor2: '#424242',
    buttonColor2Hover: '#525252',
    buttonColor2Pressed: '#363636',
    boxShadow1: '0 1px 2px -2px rgba(0, 0, 0, .24), 0 3px 6px 0 rgba(0, 0, 0, .18), 0 5px 12px 4px rgba(0, 0, 0, .12)',
    boxShadow2: '0 3px 6px -4px rgba(0, 0, 0, .24), 0 6px 16px 0 rgba(0, 0, 0, .16), 0 9px 28px 8px rgba(0, 0, 0, .10)',
    boxShadow3: '0 6px 16px -9px rgba(0, 0, 0, .08), 0 9px 28px 0 rgba(0, 0, 0, .05), 0 12px 48px 16px rgba(0, 0, 0, .03)'
  }
}

const activeKey = computed(() => route.name)

const currentPageTitle = computed(() => {
  const titles = {
    'AdminDashboard': 'Tổng quan',
    'AdminUsers': 'Quản lý người dùng',
    'AdminMessages': 'Quản lý tin nhắn',
    'AdminBlacklist': 'Danh sách đen'
  }
  return titles[route.name] || 'Trang chủ'
})

const renderIcon = (icon) => {
  return () => h(NIcon, { size: 18 }, { default: () => h('div', { innerHTML: icon }) })
}

const menuOptions = [
  {
    label: 'Tổng quan',
    key: 'AdminDashboard',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/></svg>`)
  },
  {
    label: 'Quản lý người dùng',
    key: 'AdminUsers',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>`)
  },
  {
    label: 'Quản lý tin nhắn',
    key: 'AdminMessages',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg>`)
  },
  {
    label: 'Danh sách đen',
    key: 'AdminBlacklist',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/></svg>`)
  }
]

const userMenuOptions = [
  {
    label: 'Hồ sơ cá nhân',
    key: 'profile',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>`)
  },
  {
    label: 'Cài đặt',
    key: 'settings',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/></svg>`)
  },
  {
    type: 'divider'
  },
  {
    label: 'Đăng xuất',
    key: 'logout',
    icon: renderIcon(`<svg viewBox="0 0 24 24"><path fill="currentColor" d="M16 17v-3H9v-4h7V7l5 5l-5 5M14 2a2 2 0 0 1 2 2v2h-2V4H5v16h9v-2h2v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9Z"/></svg>`)
  }
]

const handleResize = () => {
  const width = window.innerWidth
  isMobile.value = width < 768
  if (isMobile.value) {
    collapsed.value = true
  }
}

onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const handleCollapse = () => {
  collapsed.value = true
}

const handleExpand = () => {
  collapsed.value = false
}

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const closeMobileSidebar = () => {
  if (isMobile.value) {
    collapsed.value = true
  }
}

const handleLogoClick = () => {
  router.push({ name: 'AdminDashboard' })
}

const handleMenuSelect = (key) => {
  router.push({ name: key })
  if (isMobile.value) {
    collapsed.value = true
  }
}

const handleUserMenuSelect = (key) => {
  if (key === 'logout') {
    authStore.logout()
    router.push('/login')
  } else if (key === 'profile') {
    router.push('/profile')
  } else if (key === 'settings') {
    router.push('/settings')
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  width: 100vw;
  background: #121212;
  overflow: hidden;
}

.layout-container {
  height: 100%;
  width: 100%;
}

.admin-sider {
  height: 100%;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.mobile-sidebar {
  position: fixed !important;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1001;
}

.admin-sidebar {
  height: 100%;
  background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(255, 255, 255, 0.08);
}

.logo-container {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(135deg, #1e1e1e 0%, #2c2c2c 100%);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.logo:hover {
  background: rgba(100, 255, 218, 0.1);
  transform: translateY(-1px);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: linear-gradient(135deg, #64ffda 0%, #4fd3b8 100%);
  box-shadow: 0 4px 12px rgba(100, 255, 218, 0.3);
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.brand-name {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.brand-subtitle {
  font-size: 11px;
  color: #64ffda;
  font-weight: 500;
  letter-spacing: 1px;
}

.menu-scrollbar {
  flex: 1;
  padding: 8px 0;
}

.admin-menu {
  padding: 0 12px;
}

.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(0, 0, 0, 0.2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.05);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.main-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-header {
  height: 64px;
  background: linear-gradient(135deg, #1e1e1e 0%, #2c2c2c 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.header-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mobile-menu-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
  background: rgba(100, 255, 218, 0.1);
  transform: translateY(-1px);
}

.breadcrumb {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.header-btn:hover {
  background: rgba(100, 255, 218, 0.1);
  transform: translateY(-1px);
}

.user-avatar-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.user-avatar-btn:hover {
  background: rgba(100, 255, 218, 0.1);
  transform: translateY(-1px);
}

.admin-content {
  flex: 1;
  background: #121212;
  overflow-y: auto;
}

.content-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(4px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }
  
  .content-container {
    padding: 16px;
  }
  
  .breadcrumb {
    font-size: 14px;
  }
  
  .brand-name {
    font-size: 14px;
  }
  
  .brand-subtitle {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 12px;
  }
  
  .content-container {
    padding: 12px;
  }
  
  .logo-container {
    padding: 12px 16px;
  }
  
  .sidebar-footer {
    padding: 12px 16px;
  }
}
</style>