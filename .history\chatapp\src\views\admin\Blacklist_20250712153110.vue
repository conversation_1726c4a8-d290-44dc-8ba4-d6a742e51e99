<template>
  <div class="admin-blacklist">
    <!-- Header Section -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <n-icon class="title-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </n-icon>
            Danh sách đen IP
          </h1>
          <p class="page-subtitle">Quản lý các địa chỉ IP bị chặn truy cập vào hệ thống</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ blacklist.length }}</div>
            <div class="stat-label">IP bị chặn</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayBlockedCount }}</div>
            <div class="stat-label">Hôm nay</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ selectedIPs.length }}</div>
            <div class="stat-label">Đã chọn</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Actions -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-container">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm IP hoặc lý do chặn..."
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-container">
          <n-select
            v-model:value="dateFilter"
            placeholder="Lọc theo thời gian"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: '' },
              { label: 'Hôm nay', value: 'today' },
              { label: '7 ngày qua', value: 'week' },
              { label: '30 ngày qua', value: 'month' }
            ]"
          />

          <n-select
            v-model:value="adminFilter"
            placeholder="Lọc theo người thêm"
            clearable
            size="large"
            style="width: 180px;"
            :options="adminOptions"
          />
        </div>
      </div>

      <div class="toolbar-right">
        <n-button @click="fetchBlacklist" :loading="loading" size="large" class="refresh-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới
        </n-button>

        <n-button
          v-if="selectedIPs.length > 0"
          type="error"
          size="large"
          @click="showBulkDeleteModal = true"
          class="bulk-delete-btn"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"/>
              </svg>
            </n-icon>
          </template>
          Xóa đã chọn ({{ selectedIPs.length }})
        </n-button>

        <n-button type="primary" size="large" @click="showAddIPModal = true" class="add-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </n-icon>
          </template>
          Chặn IP mới
        </n-button>
      </div>
    </div>

    <!-- IP Blacklist Table -->
    <div class="table-container">
      <div class="table-header">
        <div class="bulk-actions">
          <n-checkbox
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @update:checked="handleSelectAll"
          >
            Chọn tất cả
          </n-checkbox>
          <span class="ip-count">{{ filteredBlacklist.length }} IP bị chặn</span>
        </div>
        <div class="table-info">
          <n-tag type="error" size="small">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </n-icon>
            </template>
            Bảo mật cao
          </n-tag>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="filteredBlacklist"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        size="large"
        class="modern-blacklist-table"
        :row-key="(row) => row.id"
        :checked-row-keys="selectedIPs"
        @update:checked-row-keys="selectedIPs = $event"
      />
    </div>
    
    <n-modal v-model:show="showAddIPModal" preset="dialog" title="Thêm IP vào danh sách đen">
      <n-form ref="addIPFormRef" :model="newIP" :rules="addIPRules">
        <n-form-item path="ip" label="Địa chỉ IP">
          <n-input v-model:value="newIP.ip" placeholder="Nhập địa chỉ IP (VD: ***********)" />
        </n-form-item>
        
        <n-form-item path="reason" label="Lý do chặn">
          <n-input
            v-model:value="newIP.reason"
            type="textarea"
            placeholder="Nhập lý do chặn IP này..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showAddIPModal = false">Hủy</n-button>
          <n-button type="primary" @click="handleAddIP" :loading="addingIP">
            Thêm
          </n-button>
        </n-space>
      </template>
    </n-modal>
    
    <n-modal v-model:show="showEditIPModal" preset="dialog" title="Chỉnh sửa IP">
      <n-form ref="editIPFormRef" :model="editingIP" :rules="editIPRules">
        <n-form-item path="ip" label="Địa chỉ IP">
          <n-input v-model:value="editingIP.ip" placeholder="Nhập địa chỉ IP" />
        </n-form-item>
        
        <n-form-item path="reason" label="Lý do chặn">
          <n-input
            v-model:value="editingIP.reason"
            type="textarea"
            placeholder="Nhập lý do chặn IP này..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showEditIPModal = false">Hủy</n-button>
          <n-button type="primary" @click="handleEditIP" :loading="editingIPLoading">
            Cập nhật
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NButton, NPopconfirm, NText, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const blacklist = ref([])
const loading = ref(false)
const searchQuery = ref('')

const showAddIPModal = ref(false)
const showEditIPModal = ref(false)
const addingIP = ref(false)
const editingIPLoading = ref(false)

const addIPFormRef = ref()
const editIPFormRef = ref()

const newIP = ref({
  ip: '',
  reason: ''
})

const editingIP = ref({})

const pagination = {
  pageSize: 10
}

const filteredBlacklist = computed(() => {
  if (!searchQuery.value) return blacklist.value
  
  return blacklist.value.filter(item => 
    item.ip.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.reason.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const columns = [
  {
    title: 'Địa chỉ IP',
    key: 'ip',
    width: 150
  },
  {
    title: 'Lý do chặn',
    key: 'reason',
    render: (row) => h(NText, {
      style: 'max-width: 300px; word-break: break-word;'
    }, { default: () => row.reason || 'Không có lý do' })
  },
  {
    title: 'Người thêm',
    key: 'admin_username',
    width: 120,
    render: (row) => row.admin_username || 'Hệ thống'
  },
  {
    title: 'Thời gian thêm',
    key: 'created_at',
    width: 150,
    render: (row) => new Date(row.created_at).toLocaleString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 150,
    render: (row) => h('div', { style: 'display: flex; gap: 8px;' }, [
      h(NButton, {
        size: 'small',
        onClick: () => handleEditIPClick(row)
      }, { default: () => 'Sửa' }),
      
      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteIP(row.id)
      }, {
        trigger: () => h(NButton, {
          size: 'small',
          type: 'error'
        }, { default: () => 'Xóa' }),
        default: () => 'Bạn có chắc muốn xóa IP này khỏi danh sách đen?'
      })
    ])
  }
]

const addIPRules = {
  ip: [
    { required: true, message: 'Vui lòng nhập địa chỉ IP', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'Địa chỉ IP không hợp lệ',
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: 'Vui lòng nhập lý do chặn', trigger: 'blur' }
  ]
}

const editIPRules = {
  ip: [
    { required: true, message: 'Vui lòng nhập địa chỉ IP', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'Địa chỉ IP không hợp lệ',
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: 'Vui lòng nhập lý do chặn', trigger: 'blur' }
  ]
}

const fetchBlacklist = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getBlacklist()
    if (response.data.success) {
      blacklist.value = response.data.blacklist
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách đen')
  } finally {
    loading.value = false
  }
}

const handleAddIP = async () => {
  try {
    await addIPFormRef.value?.validate()
    addingIP.value = true

    const response = await adminAPI.addToBlacklist(newIP.value)

    if (response.data.success) {
      message.success('Thêm IP vào danh sách đen thành công')
      showAddIPModal.value = false
      newIP.value = { ip: '', reason: '' }
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Thêm IP thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingIP.value = false
  }
}

const handleEditIPClick = (ip) => {
  editingIP.value = { ...ip }
  showEditIPModal.value = true
}

const handleEditIP = async () => {
  try {
    await editIPFormRef.value?.validate()
    editingIPLoading.value = true

    const response = await adminAPI.updateBlacklist(editingIP.value.id, editingIP.value)

    if (response.data.success) {
      message.success('Cập nhật IP thành công')
      showEditIPModal.value = false
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Cập nhật IP thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingIPLoading.value = false
  }
}

const handleDeleteIP = async (ipId) => {
  try {
    const response = await adminAPI.removeFromBlacklist(ipId)

    if (response.data.success) {
      message.success('Xóa IP khỏi danh sách đen thành công')
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Xóa IP thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa IP')
  }
}

onMounted(() => {
  fetchBlacklist()
})
</script>

<style scoped>
.admin-blacklist {
  max-width: 1200px;
}
</style>
