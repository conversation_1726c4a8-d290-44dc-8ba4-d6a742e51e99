<template>
  <div class="admin-blacklist">
    <!-- Header Section -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <n-icon class="title-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </n-icon>
            Danh sách đen IP
          </h1>
          <p class="page-subtitle">Quản lý các địa chỉ IP bị chặn truy cập vào hệ thống</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ blacklist.length }}</div>
            <div class="stat-label">IP bị chặn</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayBlockedCount }}</div>
            <div class="stat-label">Hôm nay</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ selectedIPs.length }}</div>
            <div class="stat-label">Đã chọn</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Actions -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-container">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm IP hoặc lý do chặn..."
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-container">
          <n-select
            v-model:value="dateFilter"
            placeholder="Lọc theo thời gian"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: '' },
              { label: 'Hôm nay', value: 'today' },
              { label: '7 ngày qua', value: 'week' },
              { label: '30 ngày qua', value: 'month' }
            ]"
          />

          <n-select
            v-model:value="adminFilter"
            placeholder="Lọc theo người thêm"
            clearable
            size="large"
            style="width: 180px;"
            :options="adminOptions"
          />
        </div>
      </div>

      <div class="toolbar-right">
        <n-button @click="fetchBlacklist" :loading="loading" size="large" class="refresh-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới
        </n-button>

        <n-button
          v-if="selectedIPs.length > 0"
          type="error"
          size="large"
          @click="showBulkDeleteModal = true"
          class="bulk-delete-btn"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"/>
              </svg>
            </n-icon>
          </template>
          Xóa đã chọn ({{ selectedIPs.length }})
        </n-button>

        <n-button type="primary" size="large" @click="showAddIPModal = true" class="add-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </n-icon>
          </template>
          Chặn IP mới
        </n-button>
      </div>
    </div>

    <!-- IP Blacklist Table -->
    <div class="table-container">
      <div class="table-header">
        <div class="bulk-actions">
          <n-checkbox
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @update:checked="handleSelectAll"
          >
            Chọn tất cả
          </n-checkbox>
          <span class="ip-count">{{ filteredBlacklist.length }} IP bị chặn</span>
        </div>
        <div class="table-info">
          <n-tag type="error" size="small">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </n-icon>
            </template>
            Bảo mật cao
          </n-tag>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="filteredBlacklist"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        size="large"
        class="modern-blacklist-table"
        :row-key="(row) => row.id"
        :checked-row-keys="selectedIPs"
        @update:checked-row-keys="selectedIPs = $event"
      />
    </div>

    <!-- Add IP Modal -->
    <n-modal v-model:show="showAddIPModal" class="custom-modal">
      <n-card
        style="width: 600px"
        title="Chặn địa chỉ IP mới"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <template #header-extra>
          <n-button quaternary circle @click="showAddIPModal = false">
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </n-icon>
          </n-button>
        </template>

        <n-form ref="addIPFormRef" :model="newIP" :rules="addIPRules" size="large" class="ip-form">
          <n-form-item path="ip" label="Địa chỉ IP">
            <n-input
              v-model:value="newIP.ip"
              placeholder="Nhập địa chỉ IP (VD: ***********)"
              :input-props="{ autocomplete: 'off' }"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="reason" label="Lý do chặn">
            <n-input
              v-model:value="newIP.reason"
              type="textarea"
              placeholder="Nhập lý do chặn IP này (spam, tấn công, vi phạm quy định...)..."
              :autosize="{ minRows: 4, maxRows: 6 }"
            />
          </n-form-item>

          <div class="ip-info-card">
            <n-alert type="warning" :show-icon="false">
              <template #header>
                <n-icon style="margin-right: 8px;">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </n-icon>
                Lưu ý quan trọng
              </template>
              IP bị chặn sẽ không thể truy cập vào hệ thống chat. Hãy chắc chắn rằng bạn muốn chặn địa chỉ IP này.
            </n-alert>
          </div>
        </n-form>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showAddIPModal = false" size="large">
              Hủy bỏ
            </n-button>
            <n-button type="error" @click="handleAddIP" :loading="addingIP" size="large">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </n-icon>
              </template>
              Chặn IP này
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- Edit IP Modal -->
    <n-modal v-model:show="showEditIPModal" class="custom-modal">
      <n-card
        style="width: 600px"
        title="Chỉnh sửa thông tin IP"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <template #header-extra>
          <n-button quaternary circle @click="showEditIPModal = false">
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </n-icon>
          </n-button>
        </template>

        <n-form ref="editIPFormRef" :model="editingIP" :rules="editIPRules" size="large" class="ip-form">
          <n-form-item path="ip" label="Địa chỉ IP">
            <n-input
              v-model:value="editingIP.ip"
              placeholder="Nhập địa chỉ IP"
              :input-props="{ autocomplete: 'off' }"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="reason" label="Lý do chặn">
            <n-input
              v-model:value="editingIP.reason"
              type="textarea"
              placeholder="Nhập lý do chặn IP này..."
              :autosize="{ minRows: 4, maxRows: 6 }"
            />
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showEditIPModal = false" size="large">
              Hủy bỏ
            </n-button>
            <n-button type="primary" @click="handleEditIP" :loading="editingIPLoading" size="large">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M21 7l-9 9l-5-5l1.41-1.41L12 13.17l7.59-7.59L21 7Z"/>
                  </svg>
                </n-icon>
              </template>
              Cập nhật
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- Bulk Delete Modal -->
    <n-modal v-model:show="showBulkDeleteModal">
      <n-card
        style="width: 500px"
        title="Xác nhận xóa IP"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <p>Bạn có chắc chắn muốn xóa {{ selectedIPs.length }} IP đã chọn khỏi danh sách đen?</p>
        <p style="color: #ff6b6b; font-weight: 500;">Các IP này sẽ có thể truy cập lại hệ thống!</p>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showBulkDeleteModal = false">Hủy bỏ</n-button>
            <n-button type="error" @click="handleBulkDelete" :loading="bulkDeleting">
              Xóa {{ selectedIPs.length }} IP
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NButton, NPopconfirm, NText, NIcon, NCheckbox, NTag, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const blacklist = ref([])
const loading = ref(false)
const searchQuery = ref('')
const dateFilter = ref('')
const adminFilter = ref('')
const selectedIPs = ref([])
const showBulkDeleteModal = ref(false)
const bulkDeleting = ref(false)

const showAddIPModal = ref(false)
const showEditIPModal = ref(false)
const addingIP = ref(false)
const editingIPLoading = ref(false)

const addIPFormRef = ref()
const editIPFormRef = ref()

const newIP = ref({
  ip: '',
  reason: ''
})

const editingIP = ref({})

const pagination = {
  pageSize: 15,
  showSizePicker: true,
  pageSizes: [10, 15, 25, 50],
  showQuickJumper: true
}

// Computed stats
const todayBlockedCount = computed(() => {
  const today = new Date().toDateString()
  return blacklist.value.filter(item =>
    new Date(item.created_at).toDateString() === today
  ).length
})

const isAllSelected = computed(() =>
  filteredBlacklist.value.length > 0 && selectedIPs.value.length === filteredBlacklist.value.length
)

const isIndeterminate = computed(() =>
  selectedIPs.value.length > 0 && selectedIPs.value.length < filteredBlacklist.value.length
)

const adminOptions = computed(() => {
  const admins = [...new Set(blacklist.value.map(item => item.admin_username).filter(Boolean))]
  return admins.map(admin => ({
    label: admin,
    value: admin
  }))
})

// Enhanced filtering
const filteredBlacklist = computed(() => {
  let filtered = blacklist.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.ip.toLowerCase().includes(query) ||
      (item.reason && item.reason.toLowerCase().includes(query)) ||
      (item.admin_username && item.admin_username.toLowerCase().includes(query))
    )
  }

  // Date filter
  if (dateFilter.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    filtered = filtered.filter(item => {
      const itemDate = new Date(item.created_at)

      switch (dateFilter.value) {
        case 'today':
          return itemDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return itemDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          return itemDate >= monthAgo
        default:
          return true
      }
    })
  }

  // Admin filter
  if (adminFilter.value) {
    filtered = filtered.filter(item => item.admin_username === adminFilter.value)
  }

  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

const columns = [
  {
    type: 'selection'
  },
  {
    title: 'Địa chỉ IP',
    key: 'ip',
    width: 180,
    render: (row) => h('div', { class: 'ip-cell' }, [
      h('div', { class: 'ip-container' }, [
        h('code', { class: 'ip-address' }, row.ip),
        h('div', { class: 'ip-status' }, [
          h(NTag, {
            type: 'error',
            size: 'small',
            style: { borderRadius: '8px', fontWeight: '500' }
          }, {
            default: () => 'Bị chặn',
            icon: () => h(NIcon, { size: 12 }, [
              h('svg', { viewBox: '0 0 24 24' }, [
                h('path', {
                  fill: 'currentColor',
                  d: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'
                })
              ])
            ])
          })
        ])
      ])
    ])
  },
  {
    title: 'Lý do chặn',
    key: 'reason',
    render: (row) => h('div', { class: 'reason-cell' }, [
      h('div', { class: 'reason-text' },
        row.reason || 'Không có lý do cụ thể'
      ),
      row.reason && row.reason.length > 100 && h('div', { class: 'reason-tooltip' }, [
        h('span', { class: 'read-more' }, 'Xem thêm...')
      ])
    ])
  },
  {
    title: 'Thông tin thêm',
    key: 'admin_info',
    width: 200,
    render: (row) => h('div', { class: 'admin-info-cell' }, [
      h('div', { class: 'admin-name' }, [
        h(NIcon, { size: 14, color: '#00d4aa', style: { marginRight: '6px' } }, [
          h('svg', { viewBox: '0 0 24 24' }, [
            h('path', {
              fill: 'currentColor',
              d: 'M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z'
            })
          ])
        ]),
        h('span', row.admin_username || 'Hệ thống')
      ]),
      h('div', { class: 'block-time' }, formatTime(row.created_at))
    ])
  },
  {
    title: 'Thời gian',
    key: 'created_at',
    width: 180,
    render: (row) => h('div', { class: 'time-cell' }, [
      h('div', { class: 'time-primary' }, formatTime(row.created_at)),
      h('div', { class: 'time-secondary' }, new Date(row.created_at).toLocaleDateString('vi-VN'))
    ])
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 160,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h(NButton, {
        size: 'medium',
        type: 'primary',
        ghost: true,
        onClick: () => handleEditIPClick(row),
        style: { marginRight: '8px' }
      }, {
        default: () => 'Chỉnh sửa',
        icon: () => h(NIcon, [
          h('svg', { viewBox: '0 0 24 24' }, [
            h('path', {
              fill: 'currentColor',
              d: 'M20.71 7.04c.39-.39.39-1.04 0-1.41l-2.34-2.34c-.37-.39-1.02-.39-1.41 0l-1.84 1.83 3.75 3.75M3 17.25V21h3.75L17.81 9.93l-3.75-3.75L3 17.25Z'
            })
          ])
        ])
      }),

      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteIP(row.id),
        positiveText: 'Xóa',
        negativeText: 'Hủy'
      }, {
        trigger: () => h(NButton, {
          size: 'medium',
          type: 'error',
          ghost: true
        }, {
          default: () => 'Bỏ chặn',
          icon: () => h(NIcon, [
            h('svg', { viewBox: '0 0 24 24' }, [
              h('path', {
                fill: 'currentColor',
                d: 'M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z'
              })
            ])
          ])
        }),
        default: () => h('div', { style: 'max-width: 300px;' }, [
          h('p', { style: 'margin: 0 0 8px 0; font-weight: 600;' }, 'Xác nhận bỏ chặn IP'),
          h('p', { style: 'margin: 0; color: rgba(255, 255, 255, 0.7);' }, `Bạn có chắc chắn muốn bỏ chặn IP "${row.ip}"? IP này sẽ có thể truy cập lại hệ thống.`)
        ])
      })
    ])
  }
]

// Helper functions
const formatTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN')
  }
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedIPs.value = filteredBlacklist.value.map(item => item.id)
  } else {
    selectedIPs.value = []
  }
}

const addIPRules = {
  ip: [
    { required: true, message: 'Vui lòng nhập địa chỉ IP', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'Địa chỉ IP không hợp lệ',
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: 'Vui lòng nhập lý do chặn', trigger: 'blur' }
  ]
}

const editIPRules = {
  ip: [
    { required: true, message: 'Vui lòng nhập địa chỉ IP', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'Địa chỉ IP không hợp lệ',
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: 'Vui lòng nhập lý do chặn', trigger: 'blur' }
  ]
}

const fetchBlacklist = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getBlacklist()
    if (response.data.success) {
      blacklist.value = response.data.blacklist
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách đen')
  } finally {
    loading.value = false
  }
}

const handleAddIP = async () => {
  try {
    await addIPFormRef.value?.validate()
    addingIP.value = true

    const response = await adminAPI.addToBlacklist(newIP.value)

    if (response.data.success) {
      message.success('Thêm IP vào danh sách đen thành công')
      showAddIPModal.value = false
      newIP.value = { ip: '', reason: '' }
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Thêm IP thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingIP.value = false
  }
}

const handleEditIPClick = (ip) => {
  editingIP.value = { ...ip }
  showEditIPModal.value = true
}

const handleEditIP = async () => {
  try {
    await editIPFormRef.value?.validate()
    editingIPLoading.value = true

    const response = await adminAPI.updateBlacklist(editingIP.value.id, editingIP.value)

    if (response.data.success) {
      message.success('Cập nhật IP thành công')
      showEditIPModal.value = false
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Cập nhật IP thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingIPLoading.value = false
  }
}

const handleDeleteIP = async (ipId) => {
  try {
    const response = await adminAPI.removeFromBlacklist(ipId)

    if (response.data.success) {
      message.success('Xóa IP khỏi danh sách đen thành công')
      fetchBlacklist()
    } else {
      message.error(response.data.message || 'Xóa IP thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa IP')
  }
}

onMounted(() => {
  fetchBlacklist()
})
</script>

<style scoped>
.admin-blacklist {
  max-width: 1200px;
}
</style>
