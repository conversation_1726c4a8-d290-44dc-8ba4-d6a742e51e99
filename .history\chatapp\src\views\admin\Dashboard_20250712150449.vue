<template>
  <div class="admin-dashboard">
    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in statsCards" :key="index">
        <div class="stat-card-content">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <n-icon size="28" :color="stat.iconColor">
              <component :is="stat.icon" />
            </n-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-change" :class="stat.changeClass">
              <n-icon size="14">
                <component :is="stat.changeIcon" />
              </n-icon>
              {{ stat.change }}
            </div>
          </div>
        </div>
        <div class="stat-chart">
          <div class="mini-chart" :style="{ background: stat.chartGradient }"></div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="dashboard-grid">
      <!-- Recent Activities -->
      <div class="dashboard-card activities-card">
        <div class="card-header">
          <div class="card-title">
            <n-icon size="20" color="#18a058">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </n-icon>
            <h3>Hoạt động gần đây</h3>
          </div>
          <n-button quaternary size="small">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </n-icon>
            </template>
            Làm mới
          </n-button>
        </div>
        <div class="card-content">
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-avatar">
                <n-avatar
                  round
                  size="medium"
                  :src="activity.avatar || '/default-avatar.png'"
                />
              </div>
              <div class="activity-content">
                <div class="activity-header">
                  <span class="activity-user">{{ activity.username }}</span>
                  <span class="activity-time">{{ formatTime(activity.time) }}</span>
                </div>
                <div class="activity-action">{{ activity.action }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Online Users -->
      <div class="dashboard-card online-users-card">
        <div class="card-header">
          <div class="card-title">
            <n-icon size="20" color="#2080f0">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4"/>
              </svg>
            </n-icon>
            <h3>Người dùng online</h3>
          </div>
          <n-badge :value="onlineUsersData.length" :max="99" type="success">
            <n-button quaternary size="small">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </n-icon>
              </template>
              Live
            </n-button>
          </n-badge>
        </div>
        <div class="card-content">
          <n-data-table
            :columns="onlineUsersColumns"
            :data="onlineUsersData"
            :pagination="{ pageSize: 5 }"
            size="small"
            :bordered="false"
            class="modern-table"
          />
        </div>
      </div>

      <!-- System Status -->
      <div class="dashboard-card system-status-card">
        <div class="card-header">
          <div class="card-title">
            <n-icon size="20" color="#f0a020">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
              </svg>
            </n-icon>
            <h3>Trạng thái hệ thống</h3>
          </div>
          <n-tag type="success" size="small">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </n-icon>
            </template>
            Hoạt động tốt
          </n-tag>
        </div>
        <div class="card-content">
          <div class="system-metrics">
            <div class="metric-item">
              <div class="metric-label">CPU Usage</div>
              <div class="metric-value">
                <n-progress type="line" :percentage="45" color="#18a058" />
                <span>45%</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Memory Usage</div>
              <div class="metric-value">
                <n-progress type="line" :percentage="62" color="#2080f0" />
                <span>62%</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Disk Usage</div>
              <div class="metric-value">
                <n-progress type="line" :percentage="38" color="#f0a020" />
                <span>38%</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Network</div>
              <div class="metric-value">
                <n-progress type="line" :percentage="78" color="#d03050" />
                <span>78%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="dashboard-card quick-actions-card">
        <div class="card-header">
          <div class="card-title">
            <n-icon size="20" color="#d03050">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
              </svg>
            </n-icon>
            <h3>Thao tác nhanh</h3>
          </div>
        </div>
        <div class="card-content">
          <div class="quick-actions">
            <n-button type="primary" size="large" block @click="$router.push({ name: 'AdminUsers' })">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M15 14c-2.67 0-8 1.33-8 4v2h16v-2c0-2.67-5.33-4-8-4m-9-4V7H4v3H1v2h3v3h2v-3h3v-2M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4"/>
                  </svg>
                </n-icon>
              </template>
              Thêm người dùng
            </n-button>
            <n-button type="info" size="large" block @click="$router.push({ name: 'AdminMessages' })">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                  </svg>
                </n-icon>
              </template>
              Xem tin nhắn
            </n-button>
            <n-button type="warning" size="large" block @click="$router.push({ name: 'AdminBlacklist' })">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/>
                  </svg>
                </n-icon>
              </template>
              Quản lý blacklist
            </n-button>
            <n-button type="error" size="large" block @click="handleEmergencyMode">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2Z"/>
                  </svg>
                </n-icon>
              </template>
              Chế độ khẩn cấp
            </n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, useMessage } from 'naive-ui'
import { adminAPI, chatAPI } from '../../utils/api'
import { useRouter } from 'vue-router'

const router = useRouter()
const message = useMessage()

const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  todayMessages: 0,
  blockedIPs: 0
})

const recentActivities = ref([])
const onlineUsersData = ref([])

// Stats Cards Configuration
const statsCards = computed(() => [
  {
    label: 'Tổng người dùng',
    value: stats.value.totalUsers,
    gradient: 'linear-gradient(135deg, #18a058 0%, #36ad6a 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(24, 160, 88, 0.1) 0%, rgba(24, 160, 88, 0.3) 100%)',
    iconColor: '#ffffff',
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4' })
    ]),
    change: '+12%',
    changeClass: 'positive',
    changeIcon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M7,14L12,9L17,14H7Z' })
    ])
  },
  {
    label: 'Người dùng online',
    value: stats.value.onlineUsers,
    gradient: 'linear-gradient(135deg, #2080f0 0%, #4098fc 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(32, 128, 240, 0.1) 0%, rgba(32, 128, 240, 0.3) 100%)',
    iconColor: '#ffffff',
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2Z' })
    ]),
    change: '+8%',
    changeClass: 'positive',
    changeIcon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M7,14L12,9L17,14H7Z' })
    ])
  },
  {
    label: 'Tin nhắn hôm nay',
    value: stats.value.todayMessages,
    gradient: 'linear-gradient(135deg, #f0a020 0%, #fcb040 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(240, 160, 32, 0.1) 0%, rgba(240, 160, 32, 0.3) 100%)',
    iconColor: '#ffffff',
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z' })
    ]),
    change: '+24%',
    changeClass: 'positive',
    changeIcon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M7,14L12,9L17,14H7Z' })
    ])
  },
  {
    label: 'IP bị chặn',
    value: stats.value.blockedIPs,
    gradient: 'linear-gradient(135deg, #d03050 0%, #e63946 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(208, 48, 80, 0.1) 0%, rgba(208, 48, 80, 0.3) 100%)',
    iconColor: '#ffffff',
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z' })
    ]),
    change: '-5%',
    changeClass: 'negative',
    changeIcon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M7,10L12,15L17,10H7Z' })
    ])
  }
])

const onlineUsersColumns = [
  {
    title: 'Avatar',
    key: 'avatar',
    width: 60,
    render: (row) => h(NAvatar, {
      round: true,
      size: 'small',
      src: row.avatar || '/default-avatar.png'
    })
  },
  {
    title: 'Tên đăng nhập',
    key: 'username',
    render: (row) => h('span', { style: 'font-weight: 500; color: #ffffff;' }, row.username)
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    width: 80,
    render: (row) => {
      return row.is_admin === '1'
        ? h(NTag, { type: 'error', size: 'small' }, { default: () => 'Admin' })
        : h(NTag, { type: 'success', size: 'small' }, { default: () => 'User' })
    }
  },
  {
    title: 'Trạng thái',
    key: 'status',
    width: 80,
    render: () => h(NTag, {
      type: 'success',
      size: 'small',
      style: 'animation: pulse 2s infinite;'
    }, { default: () => 'Online' })
  }
]

// Emergency Mode Handler
const handleEmergencyMode = () => {
  message.warning('Chế độ khẩn cấp sẽ được triển khai trong phiên bản tiếp theo')
}

const formatTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN', { 
      day: '2-digit', 
      month: '2-digit',
      year: 'numeric'
    })
  }
}

const fetchStats = async () => {
  try {
    const response = await adminAPI.getStats()
    if (response.data.success) {
      stats.value = response.data.stats
    }
  } catch (error) {
    console.error('Lỗi khi tải thống kê:', error)
  }
}

const fetchRecentActivities = async () => {
  try {
    const response = await adminAPI.getActivities()
    if (response.data.success) {
      recentActivities.value = response.data.activities
    }
  } catch (error) {
    console.error('Lỗi khi tải hoạt động:', error)
  }
}

const fetchOnlineUsers = async () => {
  try {
    const response = await chatAPI.getOnlineUsers()
    if (response.data.users) {
      onlineUsersData.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách online:', error)
  }
}

onMounted(() => {
  fetchStats()
  fetchRecentActivities()
  fetchOnlineUsers()
  
  setInterval(() => {
    fetchStats()
    fetchOnlineUsers()
  }, 30000)
})
</script>

<style scoped>
.admin-dashboard {
  max-width: 1200px;
}
</style>
