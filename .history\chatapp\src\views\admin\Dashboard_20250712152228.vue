<template>
  <div class="admin-dashboard">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          Ch<PERSON>o mừng trở lại, {{ authStore.user?.username || 'Admin' }}! 👋
        </h1>
        <p class="welcome-subtitle">
          <PERSON><PERSON><PERSON> là tổng quan về hoạt động của hệ thống Room Chat
        </p>
      </div>
      <div class="welcome-actions">
        <n-button type="primary" size="large" @click="refreshData" :loading="isLoading">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới dữ liệu
        </n-button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in statsCards" :key="index" :style="{ animationDelay: `${index * 0.1}s` }">
        <div class="stat-card-content">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <n-icon size="28" color="white">
              <component :is="stat.icon" />
            </n-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-change" :class="stat.changeType">
              <n-icon size="14">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" :d="stat.changeType === 'positive' ? 'M7 14l5-5 5 5z' : 'M7 10l5 5 5-5z'" />
                </svg>
              </n-icon>
              {{ stat.change }}
            </div>
          </div>
        </div>
        <div class="stat-chart">
          <div class="mini-chart" :style="{ background: stat.chartGradient }"></div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Recent Activities -->
      <div class="content-card activities-card">
        <div class="card-header">
          <h3 class="card-title">
            <n-icon class="card-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </n-icon>
            Hoạt động gần đây
          </h3>
          <n-button text @click="fetchRecentActivities">
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>
        <div class="activities-list">
          <div v-if="recentActivities.length === 0" class="empty-state">
            <n-icon size="48" color="#666">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </n-icon>
            <p>Chưa có hoạt động nào</p>
          </div>
          <div v-else class="activity-item" v-for="(activity, index) in recentActivities" :key="activity.id" :style="{ animationDelay: `${index * 0.05}s` }">
            <div class="activity-avatar">
              <n-avatar
                round
                size="medium"
                :src="activity.avatar || '/default-avatar.png'"
                :style="{ border: '2px solid rgba(0, 212, 170, 0.3)' }"
              />
              <div class="activity-status"></div>
            </div>
            <div class="activity-content">
              <div class="activity-header">
                <span class="activity-username">{{ activity.username }}</span>
                <span class="activity-time">{{ formatTime(activity.time) }}</span>
              </div>
              <div class="activity-action">{{ activity.action }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Performance -->
      <div class="content-card performance-card">
        <div class="card-header">
          <h3 class="card-title">
            <n-icon class="card-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
              </svg>
            </n-icon>
            Hiệu suất hệ thống
          </h3>
        </div>
        <div class="performance-metrics">
          <div class="metric-item">
            <div class="metric-label">CPU Usage</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 65%; background: linear-gradient(90deg, #00d4aa, #00b894)"></div>
            </div>
            <div class="metric-value">65%</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">Memory</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 42%; background: linear-gradient(90deg, #2080f0, #1c7ed6)"></div>
            </div>
            <div class="metric-value">42%</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">Storage</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 78%; background: linear-gradient(90deg, #f0a020, #fd7e14)"></div>
            </div>
            <div class="metric-value">78%</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">Network</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 35%; background: linear-gradient(90deg, #d03050, #e03131)"></div>
            </div>
            <div class="metric-value">35%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Online Users Table -->
    <div class="content-card table-card">
      <div class="card-header">
        <h3 class="card-title">
          <n-icon class="card-icon">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </n-icon>
          Người dùng đang online ({{ onlineUsersData.length }})
        </h3>
        <div class="table-actions">
          <n-input placeholder="Tìm kiếm..." size="small" style="width: 200px;">
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
          <n-button @click="fetchOnlineUsers">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
              </n-icon>
            </template>
            Làm mới
          </n-button>
        </div>
      </div>
      <div class="table-container">
        <n-data-table
          :columns="onlineUsersColumns"
          :data="onlineUsersData"
          :pagination="{ pageSize: 10 }"
          :bordered="false"
          size="medium"
          class="modern-table"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed } from 'vue'
import { NTag, NAvatar, NButton, NIcon } from 'naive-ui'
import { adminAPI, chatAPI } from '../../utils/api'
import { useAuthStore } from '../../stores/auth'

const authStore = useAuthStore()
const isLoading = ref(false)

const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  todayMessages: 0,
  blockedIPs: 0
})

const recentActivities = ref([])
const onlineUsersData = ref([])

// Enhanced stats cards with gradients and animations
const statsCards = computed(() => [
  {
    label: 'Tổng người dùng',
    value: stats.value.totalUsers,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' })
    ]),
    gradient: 'linear-gradient(135deg, #00d4aa 0%, #00b894 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.3) 100%)',
    change: '+12%',
    changeType: 'positive'
  },
  {
    label: 'Người dùng online',
    value: stats.value.onlineUsers,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2Z' })
    ]),
    gradient: 'linear-gradient(135deg, #2080f0 0%, #1c7ed6 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(32, 128, 240, 0.1) 0%, rgba(32, 128, 240, 0.3) 100%)',
    change: '+8%',
    changeType: 'positive'
  },
  {
    label: 'Tin nhắn hôm nay',
    value: stats.value.todayMessages,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z' })
    ]),
    gradient: 'linear-gradient(135deg, #f0a020 0%, #fd7e14 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(240, 160, 32, 0.1) 0%, rgba(240, 160, 32, 0.3) 100%)',
    change: '+24%',
    changeType: 'positive'
  },
  {
    label: 'IP bị chặn',
    value: stats.value.blockedIPs,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' })
    ]),
    gradient: 'linear-gradient(135deg, #d03050 0%, #e03131 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(208, 48, 80, 0.1) 0%, rgba(208, 48, 80, 0.3) 100%)',
    change: '-5%',
    changeType: 'negative'
  }
])

const onlineUsersColumns = [
  {
    title: 'Avatar',
    key: 'avatar',
    width: 80,
    render: (row) => h(NAvatar, {
      round: true,
      size: 'medium',
      src: row.avatar || '/default-avatar.png',
      style: { border: '2px solid rgba(0, 212, 170, 0.3)' }
    })
  },
  {
    title: 'Tên đăng nhập',
    key: 'username',
    render: (row) => h('div', { class: 'username-cell' }, [
      h('span', { class: 'username-text' }, row.username),
      h('div', { class: 'online-indicator' })
    ])
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    render: (row) => {
      return row.is_admin === '1'
        ? h(NTag, {
            type: 'error',
            size: 'small',
            style: { borderRadius: '8px', fontWeight: '500' }
          }, { default: () => 'Admin' })
        : h(NTag, {
            type: 'default',
            size: 'small',
            style: { borderRadius: '8px', fontWeight: '500' }
          }, { default: () => 'User' })
    }
  },
  {
    title: 'IP Address',
    key: 'ip',
    render: (row) => h('code', { class: 'ip-address' }, row.ip)
  },
  {
    title: 'Vị trí',
    key: 'location',
    render: (row) => h('span', { class: 'location-text' }, row.location || 'Không xác định')
  },
  {
    title: 'Lần cuối online',
    key: 'last_seen',
    render: (row) => h('span', { class: 'time-text' }, formatTime(row.last_seen))
  }
]

const formatTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }
}

const fetchStats = async () => {
  try {
    const response = await adminAPI.getStats()
    if (response.data.success) {
      stats.value = response.data.stats
    }
  } catch (error) {
    console.error('Lỗi khi tải thống kê:', error)
  }
}

const fetchRecentActivities = async () => {
  try {
    const response = await adminAPI.getActivities()
    if (response.data.success) {
      recentActivities.value = response.data.activities
    }
  } catch (error) {
    console.error('Lỗi khi tải hoạt động:', error)
  }
}

const fetchOnlineUsers = async () => {
  try {
    const response = await chatAPI.getOnlineUsers()
    if (response.data.users) {
      onlineUsersData.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách online:', error)
  }
}

const refreshData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      fetchStats(),
      fetchRecentActivities(),
      fetchOnlineUsers()
    ])
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  refreshData()

  // Auto refresh every 30 seconds
  setInterval(() => {
    fetchStats()
    fetchOnlineUsers()
  }, 30000)
})
</script>

<style scoped>
.admin-dashboard {
  max-width: 1200px;
}
</style>
