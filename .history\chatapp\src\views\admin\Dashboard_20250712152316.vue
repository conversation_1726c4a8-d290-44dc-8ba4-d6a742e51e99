<template>
  <div class="admin-dashboard">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          Ch<PERSON>o mừng trở lại, {{ authStore.user?.username || 'Admin' }}! 👋
        </h1>
        <p class="welcome-subtitle">
          <PERSON><PERSON><PERSON> là tổng quan về hoạt động của hệ thống Room Chat
        </p>
      </div>
      <div class="welcome-actions">
        <n-button type="primary" size="large" @click="refreshData" :loading="isLoading">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới dữ liệu
        </n-button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in statsCards" :key="index" :style="{ animationDelay: `${index * 0.1}s` }">
        <div class="stat-card-content">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <n-icon size="28" color="white">
              <component :is="stat.icon" />
            </n-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-change" :class="stat.changeType">
              <n-icon size="14">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" :d="stat.changeType === 'positive' ? 'M7 14l5-5 5 5z' : 'M7 10l5 5 5-5z'" />
                </svg>
              </n-icon>
              {{ stat.change }}
            </div>
          </div>
        </div>
        <div class="stat-chart">
          <div class="mini-chart" :style="{ background: stat.chartGradient }"></div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Recent Activities -->
      <div class="content-card activities-card">
        <div class="card-header">
          <h3 class="card-title">
            <n-icon class="card-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </n-icon>
            Hoạt động gần đây
          </h3>
          <n-button text @click="fetchRecentActivities">
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>
        <div class="activities-list">
          <div v-if="recentActivities.length === 0" class="empty-state">
            <n-icon size="48" color="#666">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </n-icon>
            <p>Chưa có hoạt động nào</p>
          </div>
          <div v-else class="activity-item" v-for="(activity, index) in recentActivities" :key="activity.id" :style="{ animationDelay: `${index * 0.05}s` }">
            <div class="activity-avatar">
              <n-avatar
                round
                size="medium"
                :src="activity.avatar || '/default-avatar.png'"
                :style="{ border: '2px solid rgba(0, 212, 170, 0.3)' }"
              />
              <div class="activity-status"></div>
            </div>
            <div class="activity-content">
              <div class="activity-header">
                <span class="activity-username">{{ activity.username }}</span>
                <span class="activity-time">{{ formatTime(activity.time) }}</span>
              </div>
              <div class="activity-action">{{ activity.action }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Performance -->
      <div class="content-card performance-card">
        <div class="card-header">
          <h3 class="card-title">
            <n-icon class="card-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
              </svg>
            </n-icon>
            Hiệu suất hệ thống
          </h3>
        </div>
        <div class="performance-metrics">
          <div class="metric-item">
            <div class="metric-label">CPU Usage</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 65%; background: linear-gradient(90deg, #00d4aa, #00b894)"></div>
            </div>
            <div class="metric-value">65%</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">Memory</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 42%; background: linear-gradient(90deg, #2080f0, #1c7ed6)"></div>
            </div>
            <div class="metric-value">42%</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">Storage</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 78%; background: linear-gradient(90deg, #f0a020, #fd7e14)"></div>
            </div>
            <div class="metric-value">78%</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">Network</div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 35%; background: linear-gradient(90deg, #d03050, #e03131)"></div>
            </div>
            <div class="metric-value">35%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Online Users Table -->
    <div class="content-card table-card">
      <div class="card-header">
        <h3 class="card-title">
          <n-icon class="card-icon">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </n-icon>
          Người dùng đang online ({{ onlineUsersData.length }})
        </h3>
        <div class="table-actions">
          <n-input placeholder="Tìm kiếm..." size="small" style="width: 200px;">
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
          <n-button @click="fetchOnlineUsers">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
              </n-icon>
            </template>
            Làm mới
          </n-button>
        </div>
      </div>
      <div class="table-container">
        <n-data-table
          :columns="onlineUsersColumns"
          :data="onlineUsersData"
          :pagination="{ pageSize: 10 }"
          :bordered="false"
          size="medium"
          class="modern-table"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed } from 'vue'
import { NTag, NAvatar, NButton, NIcon } from 'naive-ui'
import { adminAPI, chatAPI } from '../../utils/api'
import { useAuthStore } from '../../stores/auth'

const authStore = useAuthStore()
const isLoading = ref(false)

const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  todayMessages: 0,
  blockedIPs: 0
})

const recentActivities = ref([])
const onlineUsersData = ref([])

// Enhanced stats cards with gradients and animations
const statsCards = computed(() => [
  {
    label: 'Tổng người dùng',
    value: stats.value.totalUsers,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' })
    ]),
    gradient: 'linear-gradient(135deg, #00d4aa 0%, #00b894 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.3) 100%)',
    change: '+12%',
    changeType: 'positive'
  },
  {
    label: 'Người dùng online',
    value: stats.value.onlineUsers,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2Z' })
    ]),
    gradient: 'linear-gradient(135deg, #2080f0 0%, #1c7ed6 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(32, 128, 240, 0.1) 0%, rgba(32, 128, 240, 0.3) 100%)',
    change: '+8%',
    changeType: 'positive'
  },
  {
    label: 'Tin nhắn hôm nay',
    value: stats.value.todayMessages,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z' })
    ]),
    gradient: 'linear-gradient(135deg, #f0a020 0%, #fd7e14 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(240, 160, 32, 0.1) 0%, rgba(240, 160, 32, 0.3) 100%)',
    change: '+24%',
    changeType: 'positive'
  },
  {
    label: 'IP bị chặn',
    value: stats.value.blockedIPs,
    icon: h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' })
    ]),
    gradient: 'linear-gradient(135deg, #d03050 0%, #e03131 100%)',
    chartGradient: 'linear-gradient(90deg, rgba(208, 48, 80, 0.1) 0%, rgba(208, 48, 80, 0.3) 100%)',
    change: '-5%',
    changeType: 'negative'
  }
])

const onlineUsersColumns = [
  {
    title: 'Avatar',
    key: 'avatar',
    width: 80,
    render: (row) => h(NAvatar, {
      round: true,
      size: 'medium',
      src: row.avatar || '/default-avatar.png',
      style: { border: '2px solid rgba(0, 212, 170, 0.3)' }
    })
  },
  {
    title: 'Tên đăng nhập',
    key: 'username',
    render: (row) => h('div', { class: 'username-cell' }, [
      h('span', { class: 'username-text' }, row.username),
      h('div', { class: 'online-indicator' })
    ])
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    render: (row) => {
      return row.is_admin === '1'
        ? h(NTag, {
            type: 'error',
            size: 'small',
            style: { borderRadius: '8px', fontWeight: '500' }
          }, { default: () => 'Admin' })
        : h(NTag, {
            type: 'default',
            size: 'small',
            style: { borderRadius: '8px', fontWeight: '500' }
          }, { default: () => 'User' })
    }
  },
  {
    title: 'IP Address',
    key: 'ip',
    render: (row) => h('code', { class: 'ip-address' }, row.ip)
  },
  {
    title: 'Vị trí',
    key: 'location',
    render: (row) => h('span', { class: 'location-text' }, row.location || 'Không xác định')
  },
  {
    title: 'Lần cuối online',
    key: 'last_seen',
    render: (row) => h('span', { class: 'time-text' }, formatTime(row.last_seen))
  }
]

const formatTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }
}

const fetchStats = async () => {
  try {
    const response = await adminAPI.getStats()
    if (response.data.success) {
      stats.value = response.data.stats
    }
  } catch (error) {
    console.error('Lỗi khi tải thống kê:', error)
  }
}

const fetchRecentActivities = async () => {
  try {
    const response = await adminAPI.getActivities()
    if (response.data.success) {
      recentActivities.value = response.data.activities
    }
  } catch (error) {
    console.error('Lỗi khi tải hoạt động:', error)
  }
}

const fetchOnlineUsers = async () => {
  try {
    const response = await chatAPI.getOnlineUsers()
    if (response.data.users) {
      onlineUsersData.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách online:', error)
  }
}

const refreshData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      fetchStats(),
      fetchRecentActivities(),
      fetchOnlineUsers()
    ])
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  refreshData()

  // Auto refresh every 30 seconds
  setInterval(() => {
    fetchStats()
    fetchOnlineUsers()
  }, 30000)
})
</script>

<style scoped>
/* Dashboard Layout */
.admin-dashboard {
  width: 100%;
  animation: fadeInUp 0.6s ease-out;
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 32px;
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 184, 148, 0.05) 100%);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 170, 0.2);
  backdrop-filter: blur(10px);
}

.welcome-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.welcome-subtitle {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(24, 24, 28, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.6s ease-out both;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 212, 170, 0.3);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient, linear-gradient(90deg, #00d4aa, #00b894));
  border-radius: 20px 20px 0 0;
}

.stat-card-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #00d4aa;
}

.stat-change.negative {
  color: #ff6b6b;
}

.stat-chart {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
}

.mini-chart {
  height: 100%;
  border-radius: 4px;
  animation: chartGrow 1.5s ease-out 0.5s both;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.content-card {
  background: rgba(24, 24, 28, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.content-card:hover {
  border-color: rgba(0, 212, 170, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.card-icon {
  color: #00d4aa;
}

/* Activities Card */
.activities-list {
  padding: 16px 24px 24px 24px;
  max-height: 400px;
  overflow-y: auto;
}

.activities-list::-webkit-scrollbar {
  width: 4px;
}

.activities-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  animation: slideInLeft 0.4s ease-out both;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding-left: 12px;
  padding-right: 12px;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-avatar {
  position: relative;
}

.activity-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #00d4aa;
  border: 2px solid rgba(24, 24, 28, 1);
  border-radius: 50%;
}

.activity-content {
  flex: 1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.activity-username {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
}

.activity-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.activity-action {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

/* Performance Card */
.performance-metrics {
  padding: 16px 24px 24px 24px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  width: 80px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out;
}

.metric-value {
  width: 40px;
  text-align: right;
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
}

/* Table Card */
.table-card {
  grid-column: 1 / -1;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-container {
  padding: 0 24px 24px 24px;
}

/* Modern Table Styles */
:deep(.modern-table .n-data-table-th) {
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

:deep(.modern-table .n-data-table-td) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding: 16px 12px;
}

:deep(.modern-table .n-data-table-tr:hover .n-data-table-td) {
  background: rgba(0, 212, 170, 0.05);
}

.username-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username-text {
  font-weight: 500;
  color: #ffffff;
}

.online-indicator {
  width: 8px;
  height: 8px;
  background: #00d4aa;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.ip-address {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.location-text {
  color: rgba(255, 255, 255, 0.7);
}

.time-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes chartGrow {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }
}
</style>
