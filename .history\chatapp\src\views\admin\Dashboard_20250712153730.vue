<template>
  <div class="admin-dashboard">
    <n-grid :cols="4" :x-gap="16" :y-gap="16">
      <n-grid-item>
        <n-card>
          <n-statistic label="Tổng người dùng" :value="stats.totalUsers">
            <template #prefix>
              <n-icon color="#18a058">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2M4 1h2v6h2.5l1.5 1.5L8.5 10H6V7H4V1M12.5 11.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5M21 14v5c0 1.11-.89 2-2 2s-2-.89-2-2v-5h4"/>
                </svg>
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card>
          <n-statistic label="Người dùng online" :value="stats.onlineUsers">
            <template #prefix>
              <n-icon color="#2080f0">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2Z"/>
                </svg>
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card>
          <n-statistic label="Tin nhắn hôm nay" :value="stats.todayMessages">
            <template #prefix>
              <n-icon color="#f0a020">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                </svg>
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card>
          <n-statistic label="IP bị chặn" :value="stats.blockedIPs">
            <template #prefix>
              <n-icon color="#d03050">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/>
                </svg>
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
    </n-grid>
    
    <n-grid :cols="2" :x-gap="16" :y-gap="16" style="margin-top: 24px;">
      <n-grid-item>
        <n-card title="Hoạt động gần đây">
          <n-list>
            <n-list-item v-for="activity in recentActivities" :key="activity.id">
              <n-thing>
                <template #avatar>
                  <n-avatar
                    round
                    size="small"
                    :src="activity.avatar || '/default-avatar.png'"
                  />
                </template>
                <template #header>{{ activity.username }}</template>
                <template #description>
                  {{ activity.action }} - {{ formatTime(activity.time) }}
                </template>
              </n-thing>
            </n-list-item>
          </n-list>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card title="Thống kê tin nhắn">
          <div style="height: 300px;">
            <n-empty description="Biểu đồ sẽ được thêm vào sau" />
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>
    
    <n-card title="Người dùng online" style="margin-top: 24px;">
      <n-data-table
        :columns="onlineUsersColumns"
        :data="onlineUsersData"
        :pagination="false"
        size="small"
      />
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton } from 'naive-ui'
import { adminAPI, chatAPI } from '../../utils/api'

const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  todayMessages: 0,
  blockedIPs: 0
})

const recentActivities = ref([])
const onlineUsersData = ref([])

const onlineUsersColumns = [
  {
    title: 'Avatar',
    key: 'avatar',
    width: 80,
    render: (row) => h(NAvatar, {
      round: true,
      size: 'small',
      src: row.avatar || '/default-avatar.png'
    })
  },
  {
    title: 'Tên đăng nhập',
    key: 'username'
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    render: (row) => {
      return row.is_admin === '1' 
        ? h(NTag, { type: 'error', size: 'small' }, { default: () => 'Admin' })
        : h(NTag, { type: 'default', size: 'small' }, { default: () => 'User' })
    }
  },
  {
    title: 'IP',
    key: 'ip'
  },
  {
    title: 'Vị trí',
    key: 'location',
    render: (row) => row.location || 'Không xác định'
  },
  {
    title: 'Lần cuối online',
    key: 'last_seen',
    render: (row) => formatTime(row.last_seen)
  }
]

const formatTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN', { 
      day: '2-digit', 
      month: '2-digit',
      year: 'numeric'
    })
  }
}

const fetchStats = async () => {
  try {
    const response = await adminAPI.getStats()
    if (response.data.success) {
      stats.value = response.data.stats
    }
  } catch (error) {
    console.error('Lỗi khi tải thống kê:', error)
  }
}

const fetchRecentActivities = async () => {
  try {
    const response = await adminAPI.getActivities()
    if (response.data.success) {
      recentActivities.value = response.data.activities
    }
  } catch (error) {
    console.error('Lỗi khi tải hoạt động:', error)
  }
}

const fetchOnlineUsers = async () => {
  try {
    const response = await chatAPI.getOnlineUsers()
    if (response.data.users) {
      onlineUsersData.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách online:', error)
  }
}

onMounted(() => {
  fetchStats()
  fetchRecentActivities()
  fetchOnlineUsers()
  
  setInterval(() => {
    fetchStats()
    fetchOnlineUsers()
  }, 30000)
})
</script>

<style scoped>
.admin-dashboard {
  max-width: 1200px;
}
</style>
