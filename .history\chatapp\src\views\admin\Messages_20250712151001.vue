<template>
  <div class="admin-messages">
    <!-- Header Section -->
    <div class="messages-header">
      <div class="header-content">
        <div class="header-left">
          <h2><PERSON><PERSON><PERSON>n lý tin nhắn</h2>
          <p><PERSON> dõi và kiểm duyệt tin nhắn trong hệ thống</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ filteredMessages.length }}</div>
            <div class="stat-label">Tổng tin nhắn</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayMessages }}</div>
            <div class="stat-label">Hôm nay</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <div class="filters-content">
        <div class="search-box">
          <n-input
            v-model:value="searchQuery"
            placeholder="<PERSON><PERSON><PERSON> kiế<PERSON> tin nhắn, người dùng..."
            clearable
            size="large"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-controls">
          <n-select
            v-model:value="filterUser"
            placeholder="Lọc theo người dùng"
            clearable
            filterable
            :options="userOptions"
            size="large"
            style="width: 200px;"
          />

          <n-select
            v-model:value="dateFilter"
            placeholder="Lọc theo thời gian"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: null },
              { label: 'Hôm nay', value: 'today' },
              { label: '7 ngày qua', value: 'week' },
              { label: '30 ngày qua', value: 'month' }
            ]"
          />

          <n-button size="large" @click="fetchMessages" :loading="loading">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </n-icon>
            </template>
            Làm mới
          </n-button>

          <n-button quaternary size="large" @click="resetFilters">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                </svg>
              </n-icon>
            </template>
            Đặt lại
          </n-button>
        </div>
      </div>
    </div>

    <!-- Messages Table -->
    <div class="messages-table-container">
      <n-data-table
        :columns="columns"
        :data="filteredMessages"
        :pagination="pagination"
        :loading="loading"
        size="medium"
        :bordered="false"
        class="modern-messages-table"
        :row-class-name="getRowClassName"
      />
    </div>

    <!-- Message Preview Modal -->
    <n-modal v-model:show="showPreviewModal" class="preview-modal">
      <div class="preview-container" v-if="previewMessage">
        <div class="preview-header">
          <div class="preview-title">
            <n-icon size="24" color="#2080f0">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
              </svg>
            </n-icon>
            <h3>Chi tiết tin nhắn</h3>
          </div>
          <n-button quaternary circle @click="showPreviewModal = false">
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>

        <div class="preview-content">
          <div class="message-info">
            <div class="user-section">
              <n-avatar
                round
                size="large"
                :src="previewMessage.avatar || '/default-avatar.png'"
              />
              <div class="user-details">
                <div class="user-name">{{ previewMessage.username }}</div>
                <div class="user-meta">
                  <span class="user-role" v-if="previewMessage.is_admin === '1'">Quản trị viên</span>
                  <span class="user-ip">{{ previewMessage.ip }}</span>
                  <span class="user-location">{{ previewMessage.location || 'Không xác định' }}</span>
                </div>
              </div>
            </div>

            <div class="message-meta">
              <div class="meta-item">
                <span class="meta-label">Thời gian:</span>
                <span class="meta-value">{{ formatDateTime(previewMessage.created_at) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">ID tin nhắn:</span>
                <span class="meta-value">#{{ previewMessage.id }}</span>
              </div>
            </div>
          </div>

          <div class="message-content">
            <h4>Nội dung tin nhắn</h4>
            <div class="message-text">{{ previewMessage.content }}</div>
          </div>
        </div>

        <div class="preview-footer">
          <n-space size="large">
            <n-button size="large" @click="showPreviewModal = false">
              Đóng
            </n-button>
            <n-popconfirm @positive-click="handleDeleteMessage(previewMessage.id)">
              <template #trigger>
                <n-button type="error" size="large">
                  <template #icon>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                      </svg>
                    </n-icon>
                  </template>
                  Xóa tin nhắn
                </n-button>
              </template>
              Bạn có chắc muốn xóa tin nhắn này?
            </n-popconfirm>
          </n-space>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, NText, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const messages = ref([])
const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const filterUser = ref(null)

const pagination = {
  pageSize: 15
}

const userOptions = computed(() => {
  return users.value.map(user => ({
    label: user.username,
    value: user.id
  }))
})

const filteredMessages = computed(() => {
  let filtered = messages.value
  
  if (searchQuery.value) {
    filtered = filtered.filter(msg => 
      msg.content.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      msg.username.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
  
  if (filterUser.value) {
    filtered = filtered.filter(msg => msg.user_id === filterUser.value)
  }
  
  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

const columns = [
  {
    title: 'Người gửi',
    key: 'user',
    width: 150,
    render: (row) => h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NAvatar, {
        round: true,
        size: 'small',
        src: row.avatar || '/default-avatar.png'
      }),
      h('div', [
        h('div', { style: 'font-weight: 500;' }, row.username),
        row.is_admin === '1' && h(NTag, { 
          type: 'error', 
          size: 'tiny',
          style: 'margin-top: 2px;'
        }, { default: () => 'Admin' })
      ])
    ])
  },
  {
    title: 'Nội dung',
    key: 'content',
    render: (row) => h(NText, {
      style: 'max-width: 300px; word-break: break-word;'
    }, { default: () => row.content.length > 100 ? row.content.substring(0, 100) + '...' : row.content })
  },
  {
    title: 'IP',
    key: 'ip',
    width: 120
  },
  {
    title: 'Vị trí',
    key: 'location',
    width: 150,
    render: (row) => row.location || 'Không xác định'
  },
  {
    title: 'Thời gian',
    key: 'created_at',
    width: 150,
    render: (row) => new Date(row.created_at).toLocaleString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 100,
    render: (row) => h(NPopconfirm, {
      onPositiveClick: () => handleDeleteMessage(row.id)
    }, {
      trigger: () => h(NButton, {
        size: 'small',
        type: 'error'
      }, { default: () => 'Xóa' }),
      default: () => 'Bạn có chắc muốn xóa tin nhắn này?'
    })
  }
]

const fetchMessages = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getMessages()
    if (response.data.success) {
      messages.value = response.data.messages
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách tin nhắn')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách người dùng:', error)
  }
}

const handleDeleteMessage = async (messageId) => {
  try {
    const response = await adminAPI.deleteMessage(messageId)

    if (response.data.success) {
      message.success('Xóa tin nhắn thành công')
      fetchMessages()
    } else {
      message.error(response.data.message || 'Xóa tin nhắn thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa tin nhắn')
  }
}

onMounted(() => {
  fetchMessages()
  fetchUsers()
})
</script>

<style scoped>
.admin-messages {
  max-width: 1200px;
}
</style>
