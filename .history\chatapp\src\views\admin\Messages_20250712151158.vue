<template>
  <div class="admin-messages">
    <!-- Header Section -->
    <div class="messages-header">
      <div class="header-content">
        <div class="header-left">
          <h2><PERSON><PERSON><PERSON>n lý tin nhắn</h2>
          <p><PERSON> dõi và kiểm duyệt tin nhắn trong hệ thống</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ filteredMessages.length }}</div>
            <div class="stat-label">Tổng tin nhắn</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayMessages }}</div>
            <div class="stat-label">Hôm nay</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <div class="filters-content">
        <div class="search-box">
          <n-input
            v-model:value="searchQuery"
            placeholder="<PERSON><PERSON><PERSON> kiế<PERSON> tin nhắn, người dùng..."
            clearable
            size="large"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-controls">
          <n-select
            v-model:value="filterUser"
            placeholder="Lọc theo người dùng"
            clearable
            filterable
            :options="userOptions"
            size="large"
            style="width: 200px;"
          />

          <n-select
            v-model:value="dateFilter"
            placeholder="Lọc theo thời gian"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: null },
              { label: 'Hôm nay', value: 'today' },
              { label: '7 ngày qua', value: 'week' },
              { label: '30 ngày qua', value: 'month' }
            ]"
          />

          <n-button size="large" @click="fetchMessages" :loading="loading">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </n-icon>
            </template>
            Làm mới
          </n-button>

          <n-button quaternary size="large" @click="resetFilters">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                </svg>
              </n-icon>
            </template>
            Đặt lại
          </n-button>
        </div>
      </div>
    </div>

    <!-- Messages Table -->
    <div class="messages-table-container">
      <n-data-table
        :columns="columns"
        :data="filteredMessages"
        :pagination="pagination"
        :loading="loading"
        size="medium"
        :bordered="false"
        class="modern-messages-table"
        :row-class-name="getRowClassName"
      />
    </div>

    <!-- Message Preview Modal -->
    <n-modal v-model:show="showPreviewModal" class="preview-modal">
      <div class="preview-container" v-if="previewMessage">
        <div class="preview-header">
          <div class="preview-title">
            <n-icon size="24" color="#2080f0">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
              </svg>
            </n-icon>
            <h3>Chi tiết tin nhắn</h3>
          </div>
          <n-button quaternary circle @click="showPreviewModal = false">
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>

        <div class="preview-content">
          <div class="message-info">
            <div class="user-section">
              <n-avatar
                round
                size="large"
                :src="previewMessage.avatar || '/default-avatar.png'"
              />
              <div class="user-details">
                <div class="user-name">{{ previewMessage.username }}</div>
                <div class="user-meta">
                  <span class="user-role" v-if="previewMessage.is_admin === '1'">Quản trị viên</span>
                  <span class="user-ip">{{ previewMessage.ip }}</span>
                  <span class="user-location">{{ previewMessage.location || 'Không xác định' }}</span>
                </div>
              </div>
            </div>

            <div class="message-meta">
              <div class="meta-item">
                <span class="meta-label">Thời gian:</span>
                <span class="meta-value">{{ formatDateTime(previewMessage.created_at) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">ID tin nhắn:</span>
                <span class="meta-value">#{{ previewMessage.id }}</span>
              </div>
            </div>
          </div>

          <div class="message-content">
            <h4>Nội dung tin nhắn</h4>
            <div class="message-text">{{ previewMessage.content }}</div>
          </div>
        </div>

        <div class="preview-footer">
          <n-space size="large">
            <n-button size="large" @click="showPreviewModal = false">
              Đóng
            </n-button>
            <n-popconfirm @positive-click="handleDeleteMessage(previewMessage.id)">
              <template #trigger>
                <n-button type="error" size="large">
                  <template #icon>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                      </svg>
                    </n-icon>
                  </template>
                  Xóa tin nhắn
                </n-button>
              </template>
              Bạn có chắc muốn xóa tin nhắn này?
            </n-popconfirm>
          </n-space>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, NText, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const messages = ref([])
const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const filterUser = ref(null)
const dateFilter = ref(null)
const showPreviewModal = ref(false)
const previewMessage = ref(null)

const pagination = {
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [20, 50, 100],
  showQuickJumper: true
}

const userOptions = computed(() => {
  return users.value.map(user => ({
    label: user.username,
    value: user.id
  }))
})

const todayMessages = computed(() => {
  const today = new Date().toDateString()
  return messages.value.filter(msg =>
    new Date(msg.created_at).toDateString() === today
  ).length
})

const filteredMessages = computed(() => {
  let filtered = messages.value

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(msg =>
      msg.content.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      msg.username.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // User filter
  if (filterUser.value) {
    filtered = filtered.filter(msg => msg.user_id === filterUser.value)
  }

  // Date filter
  if (dateFilter.value) {
    const now = new Date()
    const filterDate = new Date()

    switch (dateFilter.value) {
      case 'today':
        filtered = filtered.filter(msg =>
          new Date(msg.created_at).toDateString() === now.toDateString()
        )
        break
      case 'week':
        filterDate.setDate(now.getDate() - 7)
        filtered = filtered.filter(msg =>
          new Date(msg.created_at) >= filterDate
        )
        break
      case 'month':
        filterDate.setDate(now.getDate() - 30)
        filtered = filtered.filter(msg =>
          new Date(msg.created_at) >= filterDate
        )
        break
    }
  }

  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

// Reset filters
const resetFilters = () => {
  searchQuery.value = ''
  filterUser.value = null
  dateFilter.value = null
}

// Row class name for styling
const getRowClassName = (row) => {
  if (row.is_admin === '1') return 'admin-message-row'
  return ''
}

// Show message preview
const showMessagePreview = (message) => {
  previewMessage.value = message
  showPreviewModal.value = true
}

// Format date time
const formatDateTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'
  const date = new Date(timestamp)
  return date.toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const columns = [
  {
    title: 'Người gửi',
    key: 'user',
    width: 200,
    render: (row) => h('div', { class: 'user-cell' }, [
      h(NAvatar, {
        round: true,
        size: 'medium',
        src: row.avatar || '/default-avatar.png',
        style: 'margin-right: 12px;'
      }),
      h('div', { class: 'user-info' }, [
        h('div', { class: 'user-name' }, [
          row.username,
          row.is_admin === '1' && h(NTag, {
            type: 'error',
            size: 'small',
            style: 'margin-left: 8px;'
          }, {
            default: () => 'Admin',
            icon: () => h('svg', { viewBox: '0 0 24 24', style: 'width: 12px; height: 12px;' }, [
              h('path', { fill: 'currentColor', d: 'M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.4C14.8,12.8 13.4,14.4 12,14.4C10.6,14.4 9.2,12.8 9.2,11.4V10C9.2,8.6 10.6,7 12,7Z' })
            ])
          })
        ]),
        h('div', { class: 'user-meta' }, [
          h('span', { class: 'user-ip' }, row.ip),
          h('span', { class: 'user-location' }, row.location || 'Không xác định')
        ])
      ])
    ])
  },
  {
    title: 'Nội dung tin nhắn',
    key: 'content',
    render: (row) => h('div', { class: 'message-content-cell' }, [
      h('div', {
        class: 'message-preview',
        onClick: () => showMessagePreview(row)
      }, row.content.length > 80 ? row.content.substring(0, 80) + '...' : row.content),
      h('div', { class: 'message-actions' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => showMessagePreview(row)
        }, {
          default: () => 'Xem chi tiết',
          icon: () => h('svg', { viewBox: '0 0 24 24' }, [
            h('path', { fill: 'currentColor', d: 'M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z' })
          ])
        })
      ])
    ])
  },
  {
    title: 'Thời gian',
    key: 'created_at',
    width: 160,
    render: (row) => h('div', { class: 'time-cell' }, [
      h('div', { class: 'time-primary' }, new Date(row.created_at).toLocaleDateString('vi-VN')),
      h('div', { class: 'time-secondary' }, new Date(row.created_at).toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit'
      }))
    ])
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 120,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteMessage(row.id),
        positiveText: 'Xóa',
        negativeText: 'Hủy'
      }, {
        trigger: () => h(NButton, {
          size: 'medium',
          type: 'error',
          ghost: true
        }, {
          default: () => 'Xóa',
          icon: () => h('svg', { viewBox: '0 0 24 24' }, [
            h('path', { fill: 'currentColor', d: 'M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z' })
          ])
        }),
        default: () => 'Bạn có chắc muốn xóa tin nhắn này? Hành động này không thể hoàn tác.'
      })
    ])
  }
]

const fetchMessages = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getMessages()
    if (response.data.success) {
      messages.value = response.data.messages
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách tin nhắn')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách người dùng:', error)
  }
}

const handleDeleteMessage = async (messageId) => {
  try {
    const response = await adminAPI.deleteMessage(messageId)

    if (response.data.success) {
      message.success('Xóa tin nhắn thành công')
      fetchMessages()
    } else {
      message.error(response.data.message || 'Xóa tin nhắn thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa tin nhắn')
  }
}

onMounted(() => {
  fetchMessages()
  fetchUsers()
})
</script>

<style scoped>
.admin-messages {
  padding: 0;
  max-width: none;
}

/* Header Section */
.messages-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #18a058;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* Filters Section */
.filters-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
}

.filters-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* Table Container */
.messages-table-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  overflow: hidden;
}

/* Modern Table Styles */
.modern-messages-table :deep(.n-data-table) {
  background: transparent !important;
}

.modern-messages-table :deep(.n-data-table-th) {
  background: rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding: 16px 12px !important;
}

.modern-messages-table :deep(.n-data-table-td) {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 16px 12px !important;
}

.modern-messages-table :deep(.n-data-table-tr:hover .n-data-table-td) {
  background: rgba(255, 255, 255, 0.08) !important;
}

.modern-messages-table :deep(.admin-message-row .n-data-table-td) {
  background: rgba(208, 48, 80, 0.1) !important;
}

/* User Cell */
.user-cell {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.user-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.user-ip {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Courier New', monospace;
}

.user-location {
  color: rgba(255, 255, 255, 0.5);
}

/* Message Content Cell */
.message-content-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-preview {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  word-break: break-word;
}

.message-preview:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.message-actions {
  display: flex;
  gap: 8px;
}

/* Time Cell */
.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-primary {
  font-weight: 500;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
}

.time-secondary {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

/* Preview Modal */
.preview-modal :deep(.n-modal) {
  max-width: 700px;
}

.preview-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.preview-header {
  padding: 32px 32px 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-title h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.preview-content {
  padding: 0 32px;
}

.preview-footer {
  padding: 24px 32px 32px 32px;
  display: flex;
  justify-content: flex-end;
}

/* Message Info */
.message-info {
  margin-bottom: 24px;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
}

.user-details {
  flex: 1;
}

.user-details .user-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
}

.user-details .user-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.user-role {
  color: #d03050;
  font-weight: 600;
}

.message-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.meta-label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
}

.meta-value {
  color: #ffffff;
  font-family: 'Courier New', monospace;
}

/* Message Content */
.message-content h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.message-text {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  font-size: 16px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  word-break: break-word;
  white-space: pre-wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .header-stats {
    gap: 20px;
  }

  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-controls > * {
    flex: 1;
  }

  .message-meta {
    grid-template-columns: 1fr;
  }

  .preview-header,
  .preview-content,
  .preview-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .user-section {
    flex-direction: column;
    align-items: flex-start;
    text-align: center;
  }
}

/* Animations */
.messages-header,
.filters-section,
.messages-table-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.messages-header:hover,
.filters-section:hover,
.messages-table-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}
</style>
