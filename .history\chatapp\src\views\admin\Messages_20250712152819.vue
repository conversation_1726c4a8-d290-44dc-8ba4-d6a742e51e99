<template>
  <div class="admin-messages">
    <!-- Header Section -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <n-icon class="title-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>
            </n-icon>
            Quản lý tin nhắn
          </h1>
          <p class="page-subtitle">Theo dõi và kiểm duyệt tất cả tin nhắn trong hệ thống chat</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ messages.length }}</div>
            <div class="stat-label">Tổng tin nhắn</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayMessagesCount }}</div>
            <div class="stat-label">Hôm nay</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ selectedMessages.length }}</div>
            <div class="stat-label">Đã chọn</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Actions -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-container">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm tin nhắn hoặc người gửi..."
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-container">
          <n-select
            v-model:value="filterUser"
            placeholder="Lọc theo người dùng"
            clearable
            filterable
            :options="userOptions"
            size="large"
            style="width: 200px;"
          />

          <n-select
            v-model:value="dateFilter"
            placeholder="Lọc theo thời gian"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: '' },
              { label: 'Hôm nay', value: 'today' },
              { label: '7 ngày qua', value: 'week' },
              { label: '30 ngày qua', value: 'month' }
            ]"
          />
        </div>
      </div>

      <div class="toolbar-right">
        <n-button @click="fetchMessages" :loading="loading" size="large" class="refresh-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới
        </n-button>

        <n-button
          v-if="selectedMessages.length > 0"
          type="error"
          size="large"
          @click="showBulkDeleteModal = true"
          class="bulk-delete-btn"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"/>
              </svg>
            </n-icon>
          </template>
          Xóa đã chọn ({{ selectedMessages.length }})
        </n-button>
      </div>
    </div>

    <!-- Messages Display -->
    <div class="messages-container">
      <div class="messages-header">
        <div class="bulk-actions">
          <n-checkbox
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @update:checked="handleSelectAll"
          >
            Chọn tất cả
          </n-checkbox>
          <span class="message-count">{{ filteredMessages.length }} tin nhắn</span>
        </div>
        <div class="view-toggle">
          <n-button-group>
            <n-button
              :type="viewMode === 'chat' ? 'primary' : 'default'"
              @click="viewMode = 'chat'"
            >
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2z"/>
                  </svg>
                </n-icon>
              </template>
              Chat View
            </n-button>
            <n-button
              :type="viewMode === 'table' ? 'primary' : 'default'"
              @click="viewMode = 'table'"
            >
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M3 3h18v2H3V3zm0 4h18v2H3V7zm0 4h18v2H3v-2zm0 4h18v2H3v-2zm0 4h18v2H3v-2z"/>
                  </svg>
                </n-icon>
              </template>
              Table View
            </n-button>
          </n-button-group>
        </div>
      </div>

      <!-- Chat View -->
      <div v-if="viewMode === 'chat'" class="chat-view">
        <div class="chat-messages" ref="chatContainer">
          <div
            v-for="(message, index) in filteredMessages"
            :key="message.id"
            class="message-item"
            :class="{
              'selected': selectedMessages.includes(message.id),
              'admin-message': message.is_admin === '1'
            }"
            @click="toggleMessageSelection(message.id)"
          >
            <div class="message-checkbox">
              <n-checkbox
                :checked="selectedMessages.includes(message.id)"
                @click.stop
                @update:checked="(checked) => toggleMessageSelection(message.id, checked)"
              />
            </div>

            <div class="message-avatar">
              <n-avatar
                round
                size="large"
                :src="message.avatar || '/default-avatar.png'"
                :style="{
                  border: message.is_admin === '1' ? '3px solid #00d4aa' : '3px solid rgba(255, 255, 255, 0.1)'
                }"
              />
              <div v-if="message.is_admin === '1'" class="admin-badge">
                <n-icon size="12">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </n-icon>
              </div>
            </div>

            <div class="message-content">
              <div class="message-header">
                <span class="message-username">{{ message.username }}</span>
                <div class="message-meta">
                  <span class="message-time">{{ formatTime(message.created_at) }}</span>
                  <span class="message-ip">{{ message.ip }}</span>
                  <span v-if="message.location" class="message-location">{{ message.location }}</span>
                </div>
              </div>
              <div class="message-text">{{ message.content }}</div>
              <div v-if="message.image" class="message-image">
                <img :src="message.image" alt="Hình ảnh" @click="showImageModal(message.image)" />
              </div>
            </div>

            <div class="message-actions">
              <n-button
                quaternary
                circle
                size="small"
                type="error"
                @click.stop="handleDeleteMessage(message.id)"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"/>
                  </svg>
                </n-icon>
              </n-button>
            </div>
          </div>

          <div v-if="filteredMessages.length === 0" class="empty-state">
            <n-icon size="64" color="#666">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2z"/>
              </svg>
            </n-icon>
            <h3>Không có tin nhắn nào</h3>
            <p>Chưa có tin nhắn nào phù hợp với bộ lọc hiện tại</p>
          </div>
        </div>
      </div>

      <!-- Table View -->
      <div v-else class="table-view">
        <n-data-table
          :columns="columns"
          :data="filteredMessages"
          :pagination="pagination"
          :loading="loading"
          :bordered="false"
          size="large"
          class="modern-messages-table"
          :row-key="(row) => row.id"
          :checked-row-keys="selectedMessages"
          @update:checked-row-keys="selectedMessages = $event"
        />
      </div>
    </div>

    <!-- Bulk Delete Modal -->
    <n-modal v-model:show="showBulkDeleteModal">
      <n-card
        style="width: 500px"
        title="Xác nhận xóa tin nhắn"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <p>Bạn có chắc chắn muốn xóa {{ selectedMessages.length }} tin nhắn đã chọn?</p>
        <p style="color: #ff6b6b; font-weight: 500;">Hành động này không thể hoàn tác!</p>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showBulkDeleteModal = false">Hủy bỏ</n-button>
            <n-button type="error" @click="handleBulkDelete" :loading="bulkDeleting">
              Xóa {{ selectedMessages.length }} tin nhắn
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- Image Modal -->
    <n-modal v-model:show="showImagePreview">
      <div class="image-preview-modal" @click="showImagePreview = false">
        <img :src="previewImage" alt="Preview" class="preview-image" />
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h, nextTick } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, NText, NIcon, NCheckbox, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const messages = ref([])
const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const filterUser = ref(null)
const dateFilter = ref('')
const viewMode = ref('chat')
const selectedMessages = ref([])
const showBulkDeleteModal = ref(false)
const bulkDeleting = ref(false)
const showImagePreview = ref(false)
const previewImage = ref('')
const chatContainer = ref()

const pagination = {
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true
}

const userOptions = computed(() => {
  return users.value.map(user => ({
    label: user.username,
    value: user.id
  }))
})

const todayMessagesCount = computed(() => {
  const today = new Date().toDateString()
  return messages.value.filter(msg =>
    new Date(msg.created_at).toDateString() === today
  ).length
})

const isAllSelected = computed(() =>
  filteredMessages.value.length > 0 && selectedMessages.value.length === filteredMessages.value.length
)

const isIndeterminate = computed(() =>
  selectedMessages.value.length > 0 && selectedMessages.value.length < filteredMessages.value.length
)

const filteredMessages = computed(() => {
  let filtered = messages.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(msg =>
      msg.content.toLowerCase().includes(query) ||
      msg.username.toLowerCase().includes(query)
    )
  }

  // User filter
  if (filterUser.value) {
    filtered = filtered.filter(msg => msg.user_id === filterUser.value)
  }

  // Date filter
  if (dateFilter.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    filtered = filtered.filter(msg => {
      const msgDate = new Date(msg.created_at)

      switch (dateFilter.value) {
        case 'today':
          return msgDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return msgDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          return msgDate >= monthAgo
        default:
          return true
      }
    })
  }

  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

const columns = [
  {
    title: 'Người gửi',
    key: 'user',
    width: 150,
    render: (row) => h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NAvatar, {
        round: true,
        size: 'small',
        src: row.avatar || '/default-avatar.png'
      }),
      h('div', [
        h('div', { style: 'font-weight: 500;' }, row.username),
        row.is_admin === '1' && h(NTag, { 
          type: 'error', 
          size: 'tiny',
          style: 'margin-top: 2px;'
        }, { default: () => 'Admin' })
      ])
    ])
  },
  {
    title: 'Nội dung',
    key: 'content',
    render: (row) => h(NText, {
      style: 'max-width: 300px; word-break: break-word;'
    }, { default: () => row.content.length > 100 ? row.content.substring(0, 100) + '...' : row.content })
  },
  {
    title: 'IP',
    key: 'ip',
    width: 120
  },
  {
    title: 'Vị trí',
    key: 'location',
    width: 150,
    render: (row) => row.location || 'Không xác định'
  },
  {
    title: 'Thời gian',
    key: 'created_at',
    width: 150,
    render: (row) => new Date(row.created_at).toLocaleString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 100,
    render: (row) => h(NPopconfirm, {
      onPositiveClick: () => handleDeleteMessage(row.id)
    }, {
      trigger: () => h(NButton, {
        size: 'small',
        type: 'error'
      }, { default: () => 'Xóa' }),
      default: () => 'Bạn có chắc muốn xóa tin nhắn này?'
    })
  }
]

const fetchMessages = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getMessages()
    if (response.data.success) {
      messages.value = response.data.messages
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách tin nhắn')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách người dùng:', error)
  }
}

const handleDeleteMessage = async (messageId) => {
  try {
    const response = await adminAPI.deleteMessage(messageId)

    if (response.data.success) {
      message.success('Xóa tin nhắn thành công')
      fetchMessages()
    } else {
      message.error(response.data.message || 'Xóa tin nhắn thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa tin nhắn')
  }
}

onMounted(() => {
  fetchMessages()
  fetchUsers()
})
</script>

<style scoped>
.admin-messages {
  max-width: 1200px;
}
</style>
