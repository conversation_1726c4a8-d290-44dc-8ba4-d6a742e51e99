<template>
  <div class="admin-messages">
    <!-- Header Section -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <n-icon class="title-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>
            </n-icon>
            Quản lý tin nhắn
          </h1>
          <p class="page-subtitle">Theo dõi và kiểm duyệt tất cả tin nhắn trong hệ thống chat</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ messages.length }}</div>
            <div class="stat-label">Tổng tin nhắn</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayMessagesCount }}</div>
            <div class="stat-label">Hôm nay</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ selectedMessages.length }}</div>
            <div class="stat-label">Đã chọn</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Actions -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-container">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm tin nhắn hoặc người gửi..."
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-container">
          <n-select
            v-model:value="filterUser"
            placeholder="Lọc theo người dùng"
            clearable
            filterable
            :options="userOptions"
            size="large"
            style="width: 200px;"
          />

          <n-select
            v-model:value="dateFilter"
            placeholder="Lọc theo thời gian"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: '' },
              { label: 'Hôm nay', value: 'today' },
              { label: '7 ngày qua', value: 'week' },
              { label: '30 ngày qua', value: 'month' }
            ]"
          />
        </div>
      </div>

      <div class="toolbar-right">
        <n-button @click="fetchMessages" :loading="loading" size="large" class="refresh-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới
        </n-button>

        <n-button
          v-if="selectedMessages.length > 0"
          type="error"
          size="large"
          @click="showBulkDeleteModal = true"
          class="bulk-delete-btn"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"/>
              </svg>
            </n-icon>
          </template>
          Xóa đã chọn ({{ selectedMessages.length }})
        </n-button>
      </div>
    </div>

    <!-- Messages Display -->
    <div class="messages-container">
      <div class="messages-header">
        <div class="bulk-actions">
          <n-checkbox
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @update:checked="handleSelectAll"
          >
            Chọn tất cả
          </n-checkbox>
          <span class="message-count">{{ filteredMessages.length }} tin nhắn</span>
        </div>
        <div class="view-toggle">
          <n-button-group>
            <n-button
              :type="viewMode === 'chat' ? 'primary' : 'default'"
              @click="viewMode = 'chat'"
            >
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2z"/>
                  </svg>
                </n-icon>
              </template>
              Chat View
            </n-button>
            <n-button
              :type="viewMode === 'table' ? 'primary' : 'default'"
              @click="viewMode = 'table'"
            >
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M3 3h18v2H3V3zm0 4h18v2H3V7zm0 4h18v2H3v-2zm0 4h18v2H3v-2zm0 4h18v2H3v-2z"/>
                  </svg>
                </n-icon>
              </template>
              Table View
            </n-button>
          </n-button-group>
        </div>
      </div>

      <!-- Chat View -->
      <div v-if="viewMode === 'chat'" class="chat-view">
        <div class="chat-messages" ref="chatContainer">
          <div
            v-for="(message, index) in filteredMessages"
            :key="message.id"
            class="message-item"
            :class="{
              'selected': selectedMessages.includes(message.id),
              'admin-message': message.is_admin === '1'
            }"
            @click="toggleMessageSelection(message.id)"
          >
            <div class="message-checkbox">
              <n-checkbox
                :checked="selectedMessages.includes(message.id)"
                @click.stop
                @update:checked="(checked) => toggleMessageSelection(message.id, checked)"
              />
            </div>

            <div class="message-avatar">
              <n-avatar
                round
                size="large"
                :src="message.avatar || '/default-avatar.png'"
                :style="{
                  border: message.is_admin === '1' ? '3px solid #00d4aa' : '3px solid rgba(255, 255, 255, 0.1)'
                }"
              />
              <div v-if="message.is_admin === '1'" class="admin-badge">
                <n-icon size="12">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </n-icon>
              </div>
            </div>

            <div class="message-content">
              <div class="message-header">
                <span class="message-username">{{ message.username }}</span>
                <div class="message-meta">
                  <span class="message-time">{{ formatTime(message.created_at) }}</span>
                  <span class="message-ip">{{ message.ip }}</span>
                  <span v-if="message.location" class="message-location">{{ message.location }}</span>
                </div>
              </div>
              <div class="message-text">{{ message.content }}</div>
              <div v-if="message.image" class="message-image">
                <img :src="message.image" alt="Hình ảnh" @click="showImageModal(message.image)" />
              </div>
            </div>

            <div class="message-actions">
              <n-button
                quaternary
                circle
                size="small"
                type="error"
                @click.stop="handleDeleteMessage(message.id)"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"/>
                  </svg>
                </n-icon>
              </n-button>
            </div>
          </div>

          <div v-if="filteredMessages.length === 0" class="empty-state">
            <n-icon size="64" color="#666">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2z"/>
              </svg>
            </n-icon>
            <h3>Không có tin nhắn nào</h3>
            <p>Chưa có tin nhắn nào phù hợp với bộ lọc hiện tại</p>
          </div>
        </div>
      </div>

      <!-- Table View -->
      <div v-else class="table-view">
        <n-data-table
          :columns="columns"
          :data="filteredMessages"
          :pagination="pagination"
          :loading="loading"
          :bordered="false"
          size="large"
          class="modern-messages-table"
          :row-key="(row) => row.id"
          :checked-row-keys="selectedMessages"
          @update:checked-row-keys="selectedMessages = $event"
        />
      </div>
    </div>

    <!-- Bulk Delete Modal -->
    <n-modal v-model:show="showBulkDeleteModal">
      <n-card
        style="width: 500px"
        title="Xác nhận xóa tin nhắn"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <p>Bạn có chắc chắn muốn xóa {{ selectedMessages.length }} tin nhắn đã chọn?</p>
        <p style="color: #ff6b6b; font-weight: 500;">Hành động này không thể hoàn tác!</p>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showBulkDeleteModal = false">Hủy bỏ</n-button>
            <n-button type="error" @click="handleBulkDelete" :loading="bulkDeleting">
              Xóa {{ selectedMessages.length }} tin nhắn
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- Image Modal -->
    <n-modal v-model:show="showImagePreview">
      <div class="image-preview-modal" @click="showImagePreview = false">
        <img :src="previewImage" alt="Preview" class="preview-image" />
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h, nextTick } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, NText, NIcon, NCheckbox, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const messages = ref([])
const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const filterUser = ref(null)
const dateFilter = ref('')
const viewMode = ref('chat')
const selectedMessages = ref([])
const showBulkDeleteModal = ref(false)
const bulkDeleting = ref(false)
const showImagePreview = ref(false)
const previewImage = ref('')
const chatContainer = ref()

const pagination = {
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true
}

const userOptions = computed(() => {
  return users.value.map(user => ({
    label: user.username,
    value: user.id
  }))
})

const todayMessagesCount = computed(() => {
  const today = new Date().toDateString()
  return messages.value.filter(msg =>
    new Date(msg.created_at).toDateString() === today
  ).length
})

const isAllSelected = computed(() =>
  filteredMessages.value.length > 0 && selectedMessages.value.length === filteredMessages.value.length
)

const isIndeterminate = computed(() =>
  selectedMessages.value.length > 0 && selectedMessages.value.length < filteredMessages.value.length
)

const filteredMessages = computed(() => {
  let filtered = messages.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(msg =>
      msg.content.toLowerCase().includes(query) ||
      msg.username.toLowerCase().includes(query)
    )
  }

  // User filter
  if (filterUser.value) {
    filtered = filtered.filter(msg => msg.user_id === filterUser.value)
  }

  // Date filter
  if (dateFilter.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    filtered = filtered.filter(msg => {
      const msgDate = new Date(msg.created_at)

      switch (dateFilter.value) {
        case 'today':
          return msgDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return msgDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          return msgDate >= monthAgo
        default:
          return true
      }
    })
  }

  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

const columns = [
  {
    type: 'selection'
  },
  {
    title: 'Người gửi',
    key: 'user',
    width: 200,
    render: (row) => h('div', { class: 'user-cell' }, [
      h(NAvatar, {
        round: true,
        size: 'medium',
        src: row.avatar || '/default-avatar.png',
        style: {
          border: row.is_admin === '1' ? '2px solid #00d4aa' : '2px solid rgba(255, 255, 255, 0.1)',
          marginRight: '12px'
        }
      }),
      h('div', { class: 'user-info' }, [
        h('div', { class: 'user-name' }, [
          h('span', { class: 'username' }, row.username),
          row.is_admin === '1' && h(NIcon, {
            size: 14,
            color: '#00d4aa',
            style: { marginLeft: '6px' }
          }, [
            h('svg', { viewBox: '0 0 24 24' }, [
              h('path', {
                fill: 'currentColor',
                d: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'
              })
            ])
          ])
        ]),
        h('div', { class: 'user-meta' }, [
          h('code', { class: 'ip-address' }, row.ip),
          row.location && h('span', { class: 'location' }, ` • ${row.location}`)
        ])
      ])
    ])
  },
  {
    title: 'Nội dung tin nhắn',
    key: 'content',
    render: (row) => h('div', { class: 'message-content-cell' }, [
      h('div', { class: 'message-text' },
        row.content.length > 150 ? row.content.substring(0, 150) + '...' : row.content
      ),
      row.image && h('div', { class: 'message-image-indicator' }, [
        h(NIcon, { size: 16, color: '#00d4aa' }, [
          h('svg', { viewBox: '0 0 24 24' }, [
            h('path', {
              fill: 'currentColor',
              d: 'M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'
            })
          ])
        ]),
        h('span', { style: 'margin-left: 4px; font-size: 12px; color: #00d4aa;' }, 'Có hình ảnh')
      ])
    ])
  },
  {
    title: 'Thời gian',
    key: 'created_at',
    width: 180,
    render: (row) => h('div', { class: 'time-cell' }, [
      h('div', { class: 'time-primary' }, formatTime(row.created_at)),
      h('div', { class: 'time-secondary' }, new Date(row.created_at).toLocaleDateString('vi-VN'))
    ])
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 120,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteMessage(row.id),
        positiveText: 'Xóa',
        negativeText: 'Hủy'
      }, {
        trigger: () => h(NButton, {
          size: 'medium',
          type: 'error',
          ghost: true
        }, {
          default: () => 'Xóa',
          icon: () => h(NIcon, [
            h('svg', { viewBox: '0 0 24 24' }, [
              h('path', {
                fill: 'currentColor',
                d: 'M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z'
              })
            ])
          ])
        }),
        default: () => h('div', { style: 'max-width: 300px;' }, [
          h('p', { style: 'margin: 0 0 8px 0; font-weight: 600;' }, 'Xác nhận xóa tin nhắn'),
          h('p', { style: 'margin: 0; color: rgba(255, 255, 255, 0.7);' }, `Bạn có chắc chắn muốn xóa tin nhắn này? Hành động này không thể hoàn tác.`)
        ])
      })
    ])
  }
]

// Helper functions
const formatTime = (timestamp) => {
  if (!timestamp) return 'Không xác định'

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN')
  }
}

const toggleMessageSelection = (messageId, checked = null) => {
  if (checked !== null) {
    if (checked && !selectedMessages.value.includes(messageId)) {
      selectedMessages.value.push(messageId)
    } else if (!checked && selectedMessages.value.includes(messageId)) {
      selectedMessages.value = selectedMessages.value.filter(id => id !== messageId)
    }
  } else {
    const index = selectedMessages.value.indexOf(messageId)
    if (index > -1) {
      selectedMessages.value.splice(index, 1)
    } else {
      selectedMessages.value.push(messageId)
    }
  }
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedMessages.value = filteredMessages.value.map(msg => msg.id)
  } else {
    selectedMessages.value = []
  }
}

const showImageModal = (imageUrl) => {
  previewImage.value = imageUrl
  showImagePreview.value = true
}

const fetchMessages = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getMessages()
    if (response.data.success) {
      messages.value = response.data.messages
      // Auto scroll to bottom in chat view
      if (viewMode.value === 'chat') {
        await nextTick()
        scrollToBottom()
      }
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách tin nhắn')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách người dùng:', error)
  }
}

const handleDeleteMessage = async (messageId) => {
  try {
    const response = await adminAPI.deleteMessage(messageId)

    if (response.data.success) {
      message.success('Xóa tin nhắn thành công')
      // Remove from selected if it was selected
      selectedMessages.value = selectedMessages.value.filter(id => id !== messageId)
      fetchMessages()
    } else {
      message.error(response.data.message || 'Xóa tin nhắn thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa tin nhắn')
  }
}

const handleBulkDelete = async () => {
  if (selectedMessages.value.length === 0) return

  bulkDeleting.value = true
  try {
    const deletePromises = selectedMessages.value.map(id => adminAPI.deleteMessage(id))
    await Promise.all(deletePromises)

    message.success(`Đã xóa ${selectedMessages.value.length} tin nhắn thành công`)
    selectedMessages.value = []
    showBulkDeleteModal.value = false
    fetchMessages()
  } catch (error) {
    message.error('Lỗi khi xóa tin nhắn hàng loạt')
  } finally {
    bulkDeleting.value = false
  }
}

const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

onMounted(() => {
  fetchMessages()
  fetchUsers()
})
</script>

<style scoped>
/* Page Layout */
.admin-messages {
  width: 100%;
  animation: fadeInUp 0.6s ease-out;
}

/* Header Section */
.page-header {
  margin-bottom: 32px;
  padding: 32px;
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 184, 148, 0.05) 100%);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 170, 0.2);
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.title-icon {
  color: #00d4aa;
  font-size: 32px;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #00d4aa;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* Toolbar */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: rgba(24, 24, 28, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.toolbar-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-container {
  min-width: 320px;
}

.search-input {
  border-radius: 12px;
}

.filter-container {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.refresh-btn, .bulk-delete-btn {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refresh-btn:hover, .bulk-delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.bulk-delete-btn:hover {
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

/* Messages Container */
.messages-container {
  background: rgba(24, 24, 28, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.messages-container:hover {
  border-color: rgba(0, 212, 170, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.message-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
}

.view-toggle {
  display: flex;
  gap: 8px;
}

/* Chat View */
.chat-view {
  height: 600px;
  overflow: hidden;
}

.chat-messages {
  height: 100%;
  overflow-y: auto;
  padding: 16px 24px;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.message-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
}

.message-item:hover {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.1);
}

.message-item.selected {
  background: rgba(0, 212, 170, 0.1);
  border-color: rgba(0, 212, 170, 0.3);
}

.message-item.admin-message {
  background: rgba(0, 212, 170, 0.05);
  border-left: 4px solid #00d4aa;
}

.message-checkbox {
  display: flex;
  align-items: flex-start;
  padding-top: 4px;
}

.message-avatar {
  position: relative;
  flex-shrink: 0;
}

.admin-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #00d4aa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(24, 24, 28, 1);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-username {
  font-weight: 600;
  color: #ffffff;
  font-size: 16px;
}

.message-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.message-time {
  font-weight: 500;
}

.message-ip {
  font-family: 'Courier New', monospace;
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 2px 6px;
  border-radius: 4px;
}

.message-location {
  color: rgba(255, 255, 255, 0.6);
}

.message-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  word-wrap: break-word;
  margin-bottom: 8px;
}

.message-image {
  margin-top: 8px;
}

.message-image img {
  max-width: 200px;
  max-height: 150px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.message-image img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.message-actions {
  display: flex;
  align-items: flex-start;
  padding-top: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message-item:hover .message-actions {
  opacity: 1;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state h3 {
  margin: 16px 0 8px 0;
  color: rgba(255, 255, 255, 0.7);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Table View */
.table-view {
  padding: 0 24px 24px 24px;
}

/* Modern Table Styles */
:deep(.modern-messages-table .n-data-table-th) {
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 700;
  color: #ffffff;
  font-size: 14px;
  padding: 20px 16px;
}

:deep(.modern-messages-table .n-data-table-td) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding: 20px 16px;
  background: transparent;
}

:deep(.modern-messages-table .n-data-table-tr:hover .n-data-table-td) {
  background: rgba(0, 212, 170, 0.05);
}

/* Table Cell Styles */
.user-cell {
  display: flex;
  align-items: center;
}

.user-info {
  flex: 1;
}

.user-name {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.username {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
}

.user-meta {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.ip-address {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.location {
  color: rgba(255, 255, 255, 0.6);
}

.message-content-cell {
  max-width: 400px;
}

.message-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  word-wrap: break-word;
  margin-bottom: 8px;
}

.message-image-indicator {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #00d4aa;
}

.time-cell {
  text-align: right;
}

.time-primary {
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 2px;
}

.time-secondary {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* Modal Styles */
.modal-card {
  background: rgba(24, 24, 28, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

:deep(.modal-card .n-card-header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px 24px 16px 24px;
}

:deep(.modal-card .n-card-header .n-card-header__main) {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

:deep(.modal-card .n-card__content) {
  padding: 24px;
  color: rgba(255, 255, 255, 0.9);
}

:deep(.modal-card .n-card__footer) {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-actions .n-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 0 24px;
}

/* Image Preview Modal */
.image-preview-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  cursor: pointer;
}

.preview-image {
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left {
    width: 100%;
    justify-content: center;
  }

  .messages-header {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 20px;
  }

  .toolbar {
    padding: 16px;
  }

  .search-container {
    min-width: auto;
    width: 100%;
  }

  .filter-container {
    width: 100%;
    justify-content: center;
  }

  .header-stats {
    gap: 16px;
  }

  .stat-value {
    font-size: 24px;
  }

  .page-title {
    font-size: 24px;
  }

  .chat-view {
    height: 500px;
  }

  .message-item {
    padding: 12px;
    gap: 12px;
  }

  .message-username {
    font-size: 14px;
  }
}
</style>
