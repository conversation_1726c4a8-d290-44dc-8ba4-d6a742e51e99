<template>
  <div class="admin-messages">
    <n-card title="Quản lý tin nhắn">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchQuery"
            placeholder="<PERSON><PERSON><PERSON> kiếm tin nhắn..."
            clearable
            style="width: 200px;"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
          
          <n-select
            v-model:value="filterUser"
            placeholder="<PERSON><PERSON><PERSON> theo người dùng"
            clearable
            filterable
            :options="userOptions"
            style="width: 200px;"
          />
          
          <n-button @click="fetchMessages" :loading="loading">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </n-icon>
            </template>
            Làm mới
          </n-button>
        </n-space>
      </template>
      
      <n-data-table
        :columns="columns"
        :data="filteredMessages"
        :pagination="pagination"
        :loading="loading"
        size="small"
      />
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, NText, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const messages = ref([])
const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const filterUser = ref(null)

const pagination = {
  pageSize: 15
}

const userOptions = computed(() => {
  return users.value.map(user => ({
    label: user.username,
    value: user.id
  }))
})

const filteredMessages = computed(() => {
  let filtered = messages.value
  
  if (searchQuery.value) {
    filtered = filtered.filter(msg => 
      msg.content.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      msg.username.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
  
  if (filterUser.value) {
    filtered = filtered.filter(msg => msg.user_id === filterUser.value)
  }
  
  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

const columns = [
  {
    title: 'Người gửi',
    key: 'user',
    width: 150,
    render: (row) => h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NAvatar, {
        round: true,
        size: 'small',
        src: row.avatar || '/default-avatar.png'
      }),
      h('div', [
        h('div', { style: 'font-weight: 500;' }, row.username),
        row.is_admin === '1' && h(NTag, { 
          type: 'error', 
          size: 'tiny',
          style: 'margin-top: 2px;'
        }, { default: () => 'Admin' })
      ])
    ])
  },
  {
    title: 'Nội dung',
    key: 'content',
    render: (row) => h(NText, {
      style: 'max-width: 300px; word-break: break-word;'
    }, { default: () => row.content.length > 100 ? row.content.substring(0, 100) + '...' : row.content })
  },
  {
    title: 'IP',
    key: 'ip',
    width: 120
  },
  {
    title: 'Vị trí',
    key: 'location',
    width: 150,
    render: (row) => row.location || 'Không xác định'
  },
  {
    title: 'Thời gian',
    key: 'created_at',
    width: 150,
    render: (row) => new Date(row.created_at).toLocaleString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 100,
    render: (row) => h(NPopconfirm, {
      onPositiveClick: () => handleDeleteMessage(row.id)
    }, {
      trigger: () => h(NButton, {
        size: 'small',
        type: 'error'
      }, { default: () => 'Xóa' }),
      default: () => 'Bạn có chắc muốn xóa tin nhắn này?'
    })
  }
]

const fetchMessages = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getMessages()
    if (response.data.success) {
      messages.value = response.data.messages
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách tin nhắn')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách người dùng:', error)
  }
}

const handleDeleteMessage = async (messageId) => {
  try {
    const response = await adminAPI.deleteMessage(messageId)

    if (response.data.success) {
      message.success('Xóa tin nhắn thành công')
      fetchMessages()
    } else {
      message.error(response.data.message || 'Xóa tin nhắn thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa tin nhắn')
  }
}

onMounted(() => {
  fetchMessages()
  fetchUsers()
})
</script>

<style scoped>
.admin-messages {
  max-width: 1200px;
}
</style>
