<template>
  <div class="admin-users">
    <!-- Header Section -->
    <div class="users-header">
      <div class="header-content">
        <div class="header-left">
          <h2><PERSON>u<PERSON>n lý người dùng</h2>
          <p>Quản lý tài khoản và quyền hạn người dùng</p>
        </div>
        <div class="header-actions">
          <n-button type="primary" size="large" @click="showAddUserModal = true">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15 14c-2.67 0-8 1.33-8 4v2h16v-2c0-2.67-5.33-4-8-4m-9-4V7H4v3H1v2h3v3h2v-3h3v-2M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4"/>
                </svg>
              </n-icon>
            </template>
            Thêm ng<PERSON><PERSON>i dùng
          </n-button>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <div class="filters-content">
        <div class="search-box">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm theo tên, email..."
            clearable
            size="large"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-controls">
          <n-select
            v-model:value="roleFilter"
            placeholder="Lọc theo vai trò"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: null },
              { label: 'Quản trị viên', value: '1' },
              { label: 'Người dùng', value: '0' }
            ]"
          />

          <n-select
            v-model:value="statusFilter"
            placeholder="Lọc theo trạng thái"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: null },
              { label: 'Hoạt động', value: '1' },
              { label: 'Bị khóa', value: '0' }
            ]"
          />

          <n-button quaternary size="large" @click="resetFilters">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </n-icon>
            </template>
            Đặt lại
          </n-button>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="users-table-container">
      <n-data-table
        :columns="columns"
        :data="filteredUsers"
        :pagination="pagination"
        :loading="loading"
        size="medium"
        :bordered="false"
        class="modern-users-table"
        :row-class-name="getRowClassName"
      />
    </div>

    <!-- Add User Modal -->
    <n-modal v-model:show="showAddUserModal" class="modern-modal">
      <div class="modal-container">
        <div class="modal-header">
          <div class="modal-title">
            <n-icon size="24" color="#18a058">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M15 14c-2.67 0-8 1.33-8 4v2h16v-2c0-2.67-5.33-4-8-4m-9-4V7H4v3H1v2h3v3h2v-3h3v-2M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4"/>
              </svg>
            </n-icon>
            <h3>Thêm người dùng mới</h3>
          </div>
          <n-button quaternary circle @click="showAddUserModal = false">
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>

        <div class="modal-content">
          <n-form ref="addUserFormRef" :model="newUser" :rules="addUserRules" size="large">
            <div class="form-grid">
              <n-form-item path="username" label="Tên đăng nhập">
                <n-input
                  v-model:value="newUser.username"
                  placeholder="Nhập tên đăng nhập"
                  :input-props="{ autocomplete: 'off' }"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>

              <n-form-item path="email" label="Email">
                <n-input
                  v-model:value="newUser.email"
                  placeholder="Nhập địa chỉ email"
                  type="email"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
            </div>

            <n-form-item path="password" label="Mật khẩu">
              <n-input
                v-model:value="newUser.password"
                type="password"
                placeholder="Nhập mật khẩu"
                show-password-on="click"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>

            <n-form-item path="is_admin" label="Vai trò">
              <n-select
                v-model:value="newUser.is_admin"
                :options="[
                  { label: 'Người dùng thường', value: '0' },
                  { label: 'Quản trị viên', value: '1' }
                ]"
                placeholder="Chọn vai trò"
              />
            </n-form-item>
          </n-form>
        </div>

        <div class="modal-footer">
          <n-space size="large">
            <n-button size="large" @click="showAddUserModal = false">
              Hủy bỏ
            </n-button>
            <n-button type="primary" size="large" @click="handleAddUser" :loading="addingUser">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                  </svg>
                </n-icon>
              </template>
              Thêm người dùng
            </n-button>
          </n-space>
        </div>
      </div>
    </n-modal>

    <!-- Edit User Modal -->
    <n-modal v-model:show="showEditUserModal" class="modern-modal">
      <div class="modal-container">
        <div class="modal-header">
          <div class="modal-title">
            <n-icon size="24" color="#2080f0">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
              </svg>
            </n-icon>
            <h3>Chỉnh sửa người dùng</h3>
          </div>
          <n-button quaternary circle @click="showEditUserModal = false">
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>

        <div class="modal-content">
          <n-form ref="editUserFormRef" :model="editingUser" :rules="editUserRules" size="large">
            <div class="form-grid">
              <n-form-item path="username" label="Tên đăng nhập">
                <n-input
                  v-model:value="editingUser.username"
                  placeholder="Nhập tên đăng nhập"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>

              <n-form-item path="email" label="Email">
                <n-input
                  v-model:value="editingUser.email"
                  placeholder="Nhập địa chỉ email"
                  type="email"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
            </div>

            <n-form-item path="signature" label="Chữ ký">
              <n-input
                v-model:value="editingUser.signature"
                placeholder="Nhập chữ ký cá nhân"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </n-form-item>

            <div class="form-grid">
              <n-form-item path="is_admin" label="Vai trò">
                <n-select
                  v-model:value="editingUser.is_admin"
                  :options="[
                    { label: 'Người dùng thường', value: '0' },
                    { label: 'Quản trị viên', value: '1' }
                  ]"
                />
              </n-form-item>

              <n-form-item path="status" label="Trạng thái">
                <n-select
                  v-model:value="editingUser.status"
                  :options="[
                    { label: 'Hoạt động', value: '1' },
                    { label: 'Bị khóa', value: '0' }
                  ]"
                />
              </n-form-item>
            </div>
          </n-form>
        </div>

        <div class="modal-footer">
          <n-space size="large">
            <n-button size="large" @click="showEditUserModal = false">
              Hủy bỏ
            </n-button>
            <n-button type="primary" size="large" @click="handleEditUser" :loading="editingUserLoading">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                  </svg>
                </n-icon>
              </template>
              Cập nhật
            </n-button>
          </n-space>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const users = ref([])
const loading = ref(false)
const searchQuery = ref('')

const showAddUserModal = ref(false)
const showEditUserModal = ref(false)
const addingUser = ref(false)
const editingUserLoading = ref(false)

const addUserFormRef = ref()
const editUserFormRef = ref()

const newUser = ref({
  username: '',
  email: '',
  password: '',
  is_admin: '0'
})

const editingUser = ref({})

const pagination = {
  pageSize: 10
}

const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  
  return users.value.filter(user => 
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const columns = [
  {
    title: 'Avatar',
    key: 'avatar',
    width: 80,
    render: (row) => h(NAvatar, {
      round: true,
      size: 'small',
      src: row.avatar || '/default-avatar.png'
    })
  },
  {
    title: 'Tên đăng nhập',
    key: 'username'
  },
  {
    title: 'Email',
    key: 'email'
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    render: (row) => {
      return row.is_admin === '1' 
        ? h(NTag, { type: 'error', size: 'small' }, { default: () => 'Admin' })
        : h(NTag, { type: 'default', size: 'small' }, { default: () => 'User' })
    }
  },
  {
    title: 'Trạng thái',
    key: 'status',
    render: (row) => {
      return row.status === '1' 
        ? h(NTag, { type: 'success', size: 'small' }, { default: () => 'Hoạt động' })
        : h(NTag, { type: 'error', size: 'small' }, { default: () => 'Bị khóa' })
    }
  },
  {
    title: 'Đăng ký',
    key: 'created_at',
    render: (row) => new Date(row.created_at).toLocaleDateString('vi-VN')
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 200,
    render: (row) => h('div', { style: 'display: flex; gap: 8px;' }, [
      h(NButton, {
        size: 'small',
        onClick: () => handleEditUserClick(row)
      }, { default: () => 'Sửa' }),
      
      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteUser(row.id)
      }, {
        trigger: () => h(NButton, {
          size: 'small',
          type: 'error'
        }, { default: () => 'Xóa' }),
        default: () => 'Bạn có chắc muốn xóa người dùng này?'
      })
    ])
  }
]

const addUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Vui lòng nhập mật khẩu', trigger: 'blur' },
    { min: 6, message: 'Mật khẩu tối thiểu 6 ký tự', trigger: 'blur' }
  ]
}

const editUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ]
}

const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách người dùng')
  } finally {
    loading.value = false
  }
}

const handleAddUser = async () => {
  try {
    await addUserFormRef.value?.validate()
    addingUser.value = true

    const response = await adminAPI.createUser(newUser.value)

    if (response.data.success) {
      message.success('Thêm người dùng thành công')
      showAddUserModal.value = false
      newUser.value = { username: '', email: '', password: '', is_admin: '0' }
      fetchUsers()
    } else {
      message.error(response.data.message || 'Thêm người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingUser.value = false
  }
}

const handleEditUserClick = (user) => {
  editingUser.value = { ...user }
  showEditUserModal.value = true
}

const handleEditUser = async () => {
  try {
    await editUserFormRef.value?.validate()
    editingUserLoading.value = true

    const response = await adminAPI.updateUser(editingUser.value.id, editingUser.value)

    if (response.data.success) {
      message.success('Cập nhật người dùng thành công')
      showEditUserModal.value = false
      fetchUsers()
    } else {
      message.error(response.data.message || 'Cập nhật người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingUserLoading.value = false
  }
}

const handleDeleteUser = async (userId) => {
  try {
    const response = await adminAPI.deleteUser(userId)

    if (response.data.success) {
      message.success('Xóa người dùng thành công')
      fetchUsers()
    } else {
      message.error(response.data.message || 'Xóa người dùng thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa người dùng')
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.admin-users {
  max-width: 1200px;
}
</style>
