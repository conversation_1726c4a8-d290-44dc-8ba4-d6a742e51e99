<template>
  <div class="admin-users">
    <!-- Header Section -->
    <div class="users-header">
      <div class="header-content">
        <div class="header-left">
          <h2><PERSON>u<PERSON>n lý người dùng</h2>
          <p>Quản lý tài khoản và quyền hạn người dùng</p>
        </div>
        <div class="header-actions">
          <n-button type="primary" size="large" @click="showAddUserModal = true">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15 14c-2.67 0-8 1.33-8 4v2h16v-2c0-2.67-5.33-4-8-4m-9-4V7H4v3H1v2h3v3h2v-3h3v-2M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4"/>
                </svg>
              </n-icon>
            </template>
            Thêm ng<PERSON><PERSON>i dùng
          </n-button>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <div class="filters-content">
        <div class="search-box">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm theo tên, email..."
            clearable
            size="large"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-controls">
          <n-select
            v-model:value="roleFilter"
            placeholder="Lọc theo vai trò"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: null },
              { label: 'Quản trị viên', value: '1' },
              { label: 'Người dùng', value: '0' }
            ]"
          />

          <n-select
            v-model:value="statusFilter"
            placeholder="Lọc theo trạng thái"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả', value: null },
              { label: 'Hoạt động', value: '1' },
              { label: 'Bị khóa', value: '0' }
            ]"
          />

          <n-button quaternary size="large" @click="resetFilters">
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </n-icon>
            </template>
            Đặt lại
          </n-button>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="users-table-container">
      <n-data-table
        :columns="columns"
        :data="filteredUsers"
        :pagination="pagination"
        :loading="loading"
        size="medium"
        :bordered="false"
        class="modern-users-table"
        :row-class-name="getRowClassName"
      />
    </div>

    <!-- Add User Modal -->
    <n-modal v-model:show="showAddUserModal" class="modern-modal">
      <div class="modal-container">
        <div class="modal-header">
          <div class="modal-title">
            <n-icon size="24" color="#18a058">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M15 14c-2.67 0-8 1.33-8 4v2h16v-2c0-2.67-5.33-4-8-4m-9-4V7H4v3H1v2h3v3h2v-3h3v-2M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4"/>
              </svg>
            </n-icon>
            <h3>Thêm người dùng mới</h3>
          </div>
          <n-button quaternary circle @click="showAddUserModal = false">
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>

        <div class="modal-content">
          <n-form ref="addUserFormRef" :model="newUser" :rules="addUserRules" size="large">
            <div class="form-grid">
              <n-form-item path="username" label="Tên đăng nhập">
                <n-input
                  v-model:value="newUser.username"
                  placeholder="Nhập tên đăng nhập"
                  :input-props="{ autocomplete: 'off' }"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>

              <n-form-item path="email" label="Email">
                <n-input
                  v-model:value="newUser.email"
                  placeholder="Nhập địa chỉ email"
                  type="email"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
            </div>

            <n-form-item path="password" label="Mật khẩu">
              <n-input
                v-model:value="newUser.password"
                type="password"
                placeholder="Nhập mật khẩu"
                show-password-on="click"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>

            <n-form-item path="is_admin" label="Vai trò">
              <n-select
                v-model:value="newUser.is_admin"
                :options="[
                  { label: 'Người dùng thường', value: '0' },
                  { label: 'Quản trị viên', value: '1' }
                ]"
                placeholder="Chọn vai trò"
              />
            </n-form-item>
          </n-form>
        </div>

        <div class="modal-footer">
          <n-space size="large">
            <n-button size="large" @click="showAddUserModal = false">
              Hủy bỏ
            </n-button>
            <n-button type="primary" size="large" @click="handleAddUser" :loading="addingUser">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                  </svg>
                </n-icon>
              </template>
              Thêm người dùng
            </n-button>
          </n-space>
        </div>
      </div>
    </n-modal>

    <!-- Edit User Modal -->
    <n-modal v-model:show="showEditUserModal" class="modern-modal">
      <div class="modal-container">
        <div class="modal-header">
          <div class="modal-title">
            <n-icon size="24" color="#2080f0">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
              </svg>
            </n-icon>
            <h3>Chỉnh sửa người dùng</h3>
          </div>
          <n-button quaternary circle @click="showEditUserModal = false">
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
              </svg>
            </n-icon>
          </n-button>
        </div>

        <div class="modal-content">
          <n-form ref="editUserFormRef" :model="editingUser" :rules="editUserRules" size="large">
            <div class="form-grid">
              <n-form-item path="username" label="Tên đăng nhập">
                <n-input
                  v-model:value="editingUser.username"
                  placeholder="Nhập tên đăng nhập"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>

              <n-form-item path="email" label="Email">
                <n-input
                  v-model:value="editingUser.email"
                  placeholder="Nhập địa chỉ email"
                  type="email"
                >
                  <template #prefix>
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
            </div>

            <n-form-item path="signature" label="Chữ ký">
              <n-input
                v-model:value="editingUser.signature"
                placeholder="Nhập chữ ký cá nhân"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </n-form-item>

            <div class="form-grid">
              <n-form-item path="is_admin" label="Vai trò">
                <n-select
                  v-model:value="editingUser.is_admin"
                  :options="[
                    { label: 'Người dùng thường', value: '0' },
                    { label: 'Quản trị viên', value: '1' }
                  ]"
                />
              </n-form-item>

              <n-form-item path="status" label="Trạng thái">
                <n-select
                  v-model:value="editingUser.status"
                  :options="[
                    { label: 'Hoạt động', value: '1' },
                    { label: 'Bị khóa', value: '0' }
                  ]"
                />
              </n-form-item>
            </div>
          </n-form>
        </div>

        <div class="modal-footer">
          <n-space size="large">
            <n-button size="large" @click="showEditUserModal = false">
              Hủy bỏ
            </n-button>
            <n-button type="primary" size="large" @click="handleEditUser" :loading="editingUserLoading">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                  </svg>
                </n-icon>
              </template>
              Cập nhật
            </n-button>
          </n-space>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const roleFilter = ref(null)
const statusFilter = ref(null)

const showAddUserModal = ref(false)
const showEditUserModal = ref(false)
const addingUser = ref(false)
const editingUserLoading = ref(false)

const addUserFormRef = ref()
const editUserFormRef = ref()

const newUser = ref({
  username: '',
  email: '',
  password: '',
  is_admin: '0'
})

const editingUser = ref({})

const pagination = {
  pageSize: 12,
  showSizePicker: true,
  pageSizes: [12, 24, 48],
  showQuickJumper: true
}

// Advanced filtering
const filteredUsers = computed(() => {
  let filtered = users.value

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // Role filter
  if (roleFilter.value !== null) {
    filtered = filtered.filter(user => user.is_admin === roleFilter.value)
  }

  // Status filter
  if (statusFilter.value !== null) {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }

  return filtered
})

// Reset filters
const resetFilters = () => {
  searchQuery.value = ''
  roleFilter.value = null
  statusFilter.value = null
}

// Row class name for styling
const getRowClassName = (row) => {
  if (row.status === '0') return 'disabled-row'
  if (row.is_admin === '1') return 'admin-row'
  return ''
}

const columns = [
  {
    title: 'Người dùng',
    key: 'user',
    width: 250,
    render: (row) => h('div', { class: 'user-cell' }, [
      h(NAvatar, {
        round: true,
        size: 'medium',
        src: row.avatar || '/default-avatar.png',
        style: 'margin-right: 12px;'
      }),
      h('div', { class: 'user-info' }, [
        h('div', { class: 'user-name' }, row.username),
        h('div', { class: 'user-email' }, row.email)
      ])
    ])
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    width: 120,
    render: (row) => {
      return row.is_admin === '1'
        ? h(NTag, {
            type: 'error',
            size: 'medium',
            style: 'font-weight: 600;'
          }, {
            default: () => 'Admin',
            icon: () => h('svg', { viewBox: '0 0 24 24', style: 'width: 14px; height: 14px;' }, [
              h('path', { fill: 'currentColor', d: 'M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.4C14.8,12.8 13.4,14.4 12,14.4C10.6,14.4 9.2,12.8 9.2,11.4V10C9.2,8.6 10.6,7 12,7Z' })
            ])
          })
        : h(NTag, {
            type: 'success',
            size: 'medium',
            style: 'font-weight: 600;'
          }, {
            default: () => 'User',
            icon: () => h('svg', { viewBox: '0 0 24 24', style: 'width: 14px; height: 14px;' }, [
              h('path', { fill: 'currentColor', d: 'M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z' })
            ])
          })
    }
  },
  {
    title: 'Trạng thái',
    key: 'status',
    width: 120,
    render: (row) => {
      return row.status === '1'
        ? h(NTag, {
            type: 'success',
            size: 'medium',
            style: 'font-weight: 600;'
          }, {
            default: () => 'Hoạt động',
            icon: () => h('svg', { viewBox: '0 0 24 24', style: 'width: 14px; height: 14px;' }, [
              h('path', { fill: 'currentColor', d: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' })
            ])
          })
        : h(NTag, {
            type: 'error',
            size: 'medium',
            style: 'font-weight: 600;'
          }, {
            default: () => 'Bị khóa',
            icon: () => h('svg', { viewBox: '0 0 24 24', style: 'width: 14px; height: 14px;' }, [
              h('path', { fill: 'currentColor', d: 'M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z' })
            ])
          })
    }
  },
  {
    title: 'Ngày tham gia',
    key: 'created_at',
    width: 140,
    render: (row) => h('div', { class: 'date-cell' }, [
      h('div', { class: 'date-primary' }, new Date(row.created_at).toLocaleDateString('vi-VN')),
      h('div', { class: 'date-secondary' }, new Date(row.created_at).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' }))
    ])
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 160,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h(NButton, {
        size: 'medium',
        type: 'primary',
        ghost: true,
        onClick: () => handleEditUserClick(row)
      }, {
        default: () => 'Chỉnh sửa',
        icon: () => h('svg', { viewBox: '0 0 24 24' }, [
          h('path', { fill: 'currentColor', d: 'M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z' })
        ])
      }),

      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteUser(row.id),
        positiveText: 'Xóa',
        negativeText: 'Hủy'
      }, {
        trigger: () => h(NButton, {
          size: 'medium',
          type: 'error',
          ghost: true
        }, {
          default: () => 'Xóa',
          icon: () => h('svg', { viewBox: '0 0 24 24' }, [
            h('path', { fill: 'currentColor', d: 'M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z' })
          ])
        }),
        default: () => 'Bạn có chắc muốn xóa người dùng này? Hành động này không thể hoàn tác.'
      })
    ])
  }
]

const addUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Vui lòng nhập mật khẩu', trigger: 'blur' },
    { min: 6, message: 'Mật khẩu tối thiểu 6 ký tự', trigger: 'blur' }
  ]
}

const editUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ]
}

const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách người dùng')
  } finally {
    loading.value = false
  }
}

const handleAddUser = async () => {
  try {
    await addUserFormRef.value?.validate()
    addingUser.value = true

    const response = await adminAPI.createUser(newUser.value)

    if (response.data.success) {
      message.success('Thêm người dùng thành công')
      showAddUserModal.value = false
      newUser.value = { username: '', email: '', password: '', is_admin: '0' }
      fetchUsers()
    } else {
      message.error(response.data.message || 'Thêm người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingUser.value = false
  }
}

const handleEditUserClick = (user) => {
  editingUser.value = { ...user }
  showEditUserModal.value = true
}

const handleEditUser = async () => {
  try {
    await editUserFormRef.value?.validate()
    editingUserLoading.value = true

    const response = await adminAPI.updateUser(editingUser.value.id, editingUser.value)

    if (response.data.success) {
      message.success('Cập nhật người dùng thành công')
      showEditUserModal.value = false
      fetchUsers()
    } else {
      message.error(response.data.message || 'Cập nhật người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingUserLoading.value = false
  }
}

const handleDeleteUser = async (userId) => {
  try {
    const response = await adminAPI.deleteUser(userId)

    if (response.data.success) {
      message.success('Xóa người dùng thành công')
      fetchUsers()
    } else {
      message.error(response.data.message || 'Xóa người dùng thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa người dùng')
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.admin-users {
  padding: 0;
  max-width: none;
}

/* Header Section */
.users-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* Filters Section */
.filters-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
}

.filters-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* Table Container */
.users-table-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  overflow: hidden;
}

/* Modern Table Styles */
.modern-users-table :deep(.n-data-table) {
  background: transparent !important;
}

.modern-users-table :deep(.n-data-table-th) {
  background: rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding: 16px 12px !important;
}

.modern-users-table :deep(.n-data-table-td) {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 16px 12px !important;
}

.modern-users-table :deep(.n-data-table-tr:hover .n-data-table-td) {
  background: rgba(255, 255, 255, 0.08) !important;
}

.modern-users-table :deep(.admin-row .n-data-table-td) {
  background: rgba(208, 48, 80, 0.1) !important;
}

.modern-users-table :deep(.disabled-row .n-data-table-td) {
  background: rgba(255, 255, 255, 0.02) !important;
  opacity: 0.6;
}

/* User Cell */
.user-cell {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
}

.user-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Date Cell */
.date-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.date-primary {
  font-weight: 500;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
}

.date-secondary {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

/* Modal Styles */
.modern-modal :deep(.n-modal) {
  max-width: 600px;
}

.modal-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 32px 32px 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.modal-content {
  padding: 0 32px;
}

.modal-footer {
  padding: 24px 32px 32px 32px;
  display: flex;
  justify-content: flex-end;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

/* Form Styles */
.modal-content :deep(.n-form-item-label) {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.modal-content :deep(.n-input) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
}

.modal-content :deep(.n-input:hover) {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.modal-content :deep(.n-input:focus-within) {
  border-color: #18a058 !important;
  box-shadow: 0 0 0 2px rgba(24, 160, 88, 0.2) !important;
}

.modal-content :deep(.n-input__input-el) {
  color: #ffffff !important;
}

.modal-content :deep(.n-input__input-el::placeholder) {
  color: rgba(255, 255, 255, 0.5) !important;
}

.modal-content :deep(.n-select) {
  background: rgba(255, 255, 255, 0.1) !important;
}

.modal-content :deep(.n-base-selection) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
}

.modal-content :deep(.n-base-selection:hover) {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.modal-content :deep(.n-base-selection-label) {
  color: #ffffff !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-controls > * {
    flex: 1;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}

/* Animations */
.users-header,
.filters-section,
.users-table-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.users-header:hover,
.filters-section:hover,
.users-table-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}
</style>
