<template>
  <div class="admin-users">
    <!-- Header Section -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <n-icon class="title-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </n-icon>
            Quản lý người dùng
          </h1>
          <p class="page-subtitle">Quản lý tài khoản và phân quyền người dùng trong hệ thống</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-value">{{ users.length }}</div>
            <div class="stat-label">Tổng số</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ activeUsersCount }}</div>
            <div class="stat-label">Hoạt động</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ adminUsersCount }}</div>
            <div class="stat-label">Admin</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Actions -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-container">
          <n-input
            v-model:value="searchQuery"
            placeholder="Tìm kiếm theo tên hoặc email..."
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </n-icon>
            </template>
          </n-input>
        </div>

        <div class="filter-container">
          <n-select
            v-model:value="roleFilter"
            placeholder="Lọc theo vai trò"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả vai trò', value: '' },
              { label: 'Quản trị viên', value: '1' },
              { label: 'Người dùng', value: '0' }
            ]"
          />

          <n-select
            v-model:value="statusFilter"
            placeholder="Lọc theo trạng thái"
            clearable
            size="large"
            style="width: 180px;"
            :options="[
              { label: 'Tất cả trạng thái', value: '' },
              { label: 'Hoạt động', value: '1' },
              { label: 'Bị khóa', value: '0' }
            ]"
          />
        </div>
      </div>

      <div class="toolbar-right">
        <n-button @click="fetchUsers" size="large" class="refresh-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </n-icon>
          </template>
          Làm mới
        </n-button>

        <n-button type="primary" size="large" @click="showAddUserModal = true" class="add-btn">
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </n-icon>
          </template>
          Thêm người dùng
        </n-button>
      </div>
    </div>

    <!-- Users Table -->
    <div class="table-container">
      <n-data-table
        :columns="columns"
        :data="filteredUsers"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        size="large"
        class="modern-users-table"
        :row-class-name="getRowClassName"
      />
    </div>

    <!-- Add User Modal -->
    <n-modal v-model:show="showAddUserModal" class="custom-modal">
      <n-card
        style="width: 600px"
        title="Thêm người dùng mới"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <template #header-extra>
          <n-button quaternary circle @click="showAddUserModal = false">
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </n-icon>
          </n-button>
        </template>

        <n-form ref="addUserFormRef" :model="newUser" :rules="addUserRules" size="large" class="user-form">
          <div class="form-grid">
            <n-form-item path="username" label="Tên đăng nhập">
              <n-input
                v-model:value="newUser.username"
                placeholder="Nhập tên đăng nhập"
                :input-props="{ autocomplete: 'username' }"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>

            <n-form-item path="email" label="Email">
              <n-input
                v-model:value="newUser.email"
                placeholder="Nhập địa chỉ email"
                :input-props="{ autocomplete: 'email' }"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5l-8-5V6l8 5l8-5v2z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>
          </div>

          <n-form-item path="password" label="Mật khẩu">
            <n-input
              v-model:value="newUser.password"
              type="password"
              placeholder="Nhập mật khẩu"
              show-password-on="click"
              :input-props="{ autocomplete: 'new-password' }"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 17a2 2 0 0 0 2-2a2 2 0 0 0-2-2a2 2 0 0 0-2 2a2 2 0 0 0 2 2m6-9a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V10a2 2 0 0 1 2-2h1V6a5 5 0 0 1 5-5a5 5 0 0 1 5 5v2h1m-6-5a3 3 0 0 0-3 3v2h6V6a3 3 0 0 0-3-3Z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="is_admin" label="Vai trò">
            <n-select
              v-model:value="newUser.is_admin"
              placeholder="Chọn vai trò"
              :options="[
                { label: 'Người dùng thường', value: '0' },
                { label: 'Quản trị viên', value: '1' }
              ]"
            >
              <template #arrow>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M7 10l5 5 5-5z"/>
                  </svg>
                </n-icon>
              </template>
            </n-select>
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showAddUserModal = false" size="large">
              Hủy bỏ
            </n-button>
            <n-button type="primary" @click="handleAddUser" :loading="addingUser" size="large">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                  </svg>
                </n-icon>
              </template>
              Thêm người dùng
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- Edit User Modal -->
    <n-modal v-model:show="showEditUserModal" class="custom-modal">
      <n-card
        style="width: 600px"
        title="Chỉnh sửa thông tin người dùng"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="modal-card"
      >
        <template #header-extra>
          <n-button quaternary circle @click="showEditUserModal = false">
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </n-icon>
          </n-button>
        </template>

        <n-form ref="editUserFormRef" :model="editingUser" :rules="editUserRules" size="large" class="user-form">
          <div class="form-grid">
            <n-form-item path="username" label="Tên đăng nhập">
              <n-input
                v-model:value="editingUser.username"
                placeholder="Nhập tên đăng nhập"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>

            <n-form-item path="email" label="Email">
              <n-input
                v-model:value="editingUser.email"
                placeholder="Nhập địa chỉ email"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5l-8-5V6l8 5l8-5v2z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>
          </div>

          <n-form-item path="signature" label="Chữ ký">
            <n-input
              v-model:value="editingUser.signature"
              placeholder="Nhập chữ ký cá nhân"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </n-form-item>

          <div class="form-grid">
            <n-form-item path="is_admin" label="Vai trò">
              <n-select
                v-model:value="editingUser.is_admin"
                placeholder="Chọn vai trò"
                :options="[
                  { label: 'Người dùng thường', value: '0' },
                  { label: 'Quản trị viên', value: '1' }
                ]"
              />
            </n-form-item>

            <n-form-item path="status" label="Trạng thái">
              <n-select
                v-model:value="editingUser.status"
                placeholder="Chọn trạng thái"
                :options="[
                  { label: 'Hoạt động', value: '1' },
                  { label: 'Bị khóa', value: '0' }
                ]"
              />
            </n-form-item>
          </div>
        </n-form>

        <template #footer>
          <div class="modal-actions">
            <n-button @click="showEditUserModal = false" size="large">
              Hủy bỏ
            </n-button>
            <n-button type="primary" @click="handleEditUser" :loading="editingUserLoading" size="large">
              <template #icon>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M21 7l-9 9l-5-5l1.41-1.41L12 13.17l7.59-7.59L21 7Z"/>
                  </svg>
                </n-icon>
              </template>
              Cập nhật
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { NTag, NAvatar, NButton, NPopconfirm, NIcon, useMessage } from 'naive-ui'
import { adminAPI } from '../../utils/api'

const message = useMessage()

const users = ref([])
const loading = ref(false)
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')

const showAddUserModal = ref(false)
const showEditUserModal = ref(false)
const addingUser = ref(false)
const editingUserLoading = ref(false)

const addUserFormRef = ref()
const editUserFormRef = ref()

const newUser = ref({
  username: '',
  email: '',
  password: '',
  is_admin: '0'
})

const editingUser = ref({})

const pagination = {
  pageSize: 15,
  showSizePicker: true,
  pageSizes: [10, 15, 25, 50],
  showQuickJumper: true
}

// Computed stats
const activeUsersCount = computed(() =>
  users.value.filter(user => user.status === '1').length
)

const adminUsersCount = computed(() =>
  users.value.filter(user => user.is_admin === '1').length
)

// Enhanced filtering
const filteredUsers = computed(() => {
  let filtered = users.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    )
  }

  // Role filter
  if (roleFilter.value) {
    filtered = filtered.filter(user => user.is_admin === roleFilter.value)
  }

  // Status filter
  if (statusFilter.value) {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }

  return filtered
})

// Row class name for styling
const getRowClassName = (row) => {
  if (row.status === '0') return 'disabled-row'
  if (row.is_admin === '1') return 'admin-row'
  return ''
}

const columns = [
  {
    title: 'Người dùng',
    key: 'user',
    width: 280,
    render: (row) => h('div', { class: 'user-cell' }, [
      h(NAvatar, {
        round: true,
        size: 'large',
        src: row.avatar || '/default-avatar.png',
        style: {
          border: row.is_admin === '1' ? '3px solid #00d4aa' : '3px solid rgba(255, 255, 255, 0.1)',
          marginRight: '16px'
        }
      }),
      h('div', { class: 'user-info' }, [
        h('div', { class: 'user-name' }, [
          h('span', { class: 'username' }, row.username),
          row.is_admin === '1' && h(NIcon, {
            size: 16,
            color: '#00d4aa',
            style: { marginLeft: '8px' }
          }, [
            h('svg', { viewBox: '0 0 24 24' }, [
              h('path', {
                fill: 'currentColor',
                d: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'
              })
            ])
          ])
        ]),
        h('div', { class: 'user-email' }, row.email),
        h('div', { class: 'user-meta' }, [
          h('span', { class: 'join-date' }, `Tham gia: ${new Date(row.created_at).toLocaleDateString('vi-VN')}`),
          row.last_seen && h('span', { class: 'last-seen' }, ` • Hoạt động: ${formatLastSeen(row.last_seen)}`)
        ])
      ])
    ])
  },
  {
    title: 'Vai trò',
    key: 'is_admin',
    width: 120,
    render: (row) => {
      return row.is_admin === '1'
        ? h(NTag, {
            type: 'error',
            size: 'medium',
            style: {
              borderRadius: '12px',
              fontWeight: '600',
              background: 'linear-gradient(135deg, #00d4aa, #00b894)',
              border: 'none',
              color: 'white'
            }
          }, {
            default: () => 'Admin',
            icon: () => h(NIcon, { size: 14 }, [
              h('svg', { viewBox: '0 0 24 24' }, [
                h('path', {
                  fill: 'currentColor',
                  d: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'
                })
              ])
            ])
          })
        : h(NTag, {
            type: 'default',
            size: 'medium',
            style: {
              borderRadius: '12px',
              fontWeight: '500',
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'rgba(255, 255, 255, 0.8)'
            }
          }, {
            default: () => 'User',
            icon: () => h(NIcon, { size: 14 }, [
              h('svg', { viewBox: '0 0 24 24' }, [
                h('path', {
                  fill: 'currentColor',
                  d: 'M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z'
                })
              ])
            ])
          })
    }
  },
  {
    title: 'Trạng thái',
    key: 'status',
    width: 140,
    render: (row) => {
      return row.status === '1'
        ? h('div', { class: 'status-cell active' }, [
            h('div', { class: 'status-indicator active' }),
            h('span', 'Hoạt động')
          ])
        : h('div', { class: 'status-cell inactive' }, [
            h('div', { class: 'status-indicator inactive' }),
            h('span', 'Bị khóa')
          ])
    }
  },
  {
    title: 'Thao tác',
    key: 'actions',
    width: 200,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h(NButton, {
        size: 'medium',
        type: 'primary',
        ghost: true,
        onClick: () => handleEditUserClick(row),
        style: { marginRight: '8px' }
      }, {
        default: () => 'Chỉnh sửa',
        icon: () => h(NIcon, [
          h('svg', { viewBox: '0 0 24 24' }, [
            h('path', {
              fill: 'currentColor',
              d: 'M20.71 7.04c.39-.39.39-1.04 0-1.41l-2.34-2.34c-.37-.39-1.02-.39-1.41 0l-1.84 1.83 3.75 3.75M3 17.25V21h3.75L17.81 9.93l-3.75-3.75L3 17.25Z'
            })
          ])
        ])
      }),

      h(NPopconfirm, {
        onPositiveClick: () => handleDeleteUser(row.id),
        positiveText: 'Xóa',
        negativeText: 'Hủy'
      }, {
        trigger: () => h(NButton, {
          size: 'medium',
          type: 'error',
          ghost: true
        }, {
          default: () => 'Xóa',
          icon: () => h(NIcon, [
            h('svg', { viewBox: '0 0 24 24' }, [
              h('path', {
                fill: 'currentColor',
                d: 'M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z'
              })
            ])
          ])
        }),
        default: () => h('div', { style: 'max-width: 300px;' }, [
          h('p', { style: 'margin: 0 0 8px 0; font-weight: 600;' }, 'Xác nhận xóa người dùng'),
          h('p', { style: 'margin: 0; color: rgba(255, 255, 255, 0.7);' }, `Bạn có chắc chắn muốn xóa người dùng "${row.username}"? Hành động này không thể hoàn tác.`)
        ])
      })
    ])
  }
]

// Helper function for last seen formatting
const formatLastSeen = (timestamp) => {
  if (!timestamp) return 'Chưa xác định'

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return 'Vừa xong'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} phút trước`
  } else if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('vi-VN')
  }
}

const addUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Vui lòng nhập mật khẩu', trigger: 'blur' },
    { min: 6, message: 'Mật khẩu tối thiểu 6 ký tự', trigger: 'blur' }
  ]
}

const editUserRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 20, message: 'Tên đăng nhập từ 3-20 ký tự', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Vui lòng nhập email', trigger: 'blur' },
    { type: 'email', message: 'Email không hợp lệ', trigger: 'blur' }
  ]
}

const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getUsers()
    if (response.data.success) {
      users.value = response.data.users
    }
  } catch (error) {
    message.error('Lỗi khi tải danh sách người dùng')
  } finally {
    loading.value = false
  }
}

const handleAddUser = async () => {
  try {
    await addUserFormRef.value?.validate()
    addingUser.value = true

    const response = await adminAPI.createUser(newUser.value)

    if (response.data.success) {
      message.success('Thêm người dùng thành công')
      showAddUserModal.value = false
      newUser.value = { username: '', email: '', password: '', is_admin: '0' }
      fetchUsers()
    } else {
      message.error(response.data.message || 'Thêm người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    addingUser.value = false
  }
}

const handleEditUserClick = (user) => {
  editingUser.value = { ...user }
  showEditUserModal.value = true
}

const handleEditUser = async () => {
  try {
    await editUserFormRef.value?.validate()
    editingUserLoading.value = true

    const response = await adminAPI.updateUser(editingUser.value.id, editingUser.value)

    if (response.data.success) {
      message.success('Cập nhật người dùng thành công')
      showEditUserModal.value = false
      fetchUsers()
    } else {
      message.error(response.data.message || 'Cập nhật người dùng thất bại')
    }
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    editingUserLoading.value = false
  }
}

const handleDeleteUser = async (userId) => {
  try {
    const response = await adminAPI.deleteUser(userId)

    if (response.data.success) {
      message.success('Xóa người dùng thành công')
      fetchUsers()
    } else {
      message.error(response.data.message || 'Xóa người dùng thất bại')
    }
  } catch (error) {
    message.error('Lỗi khi xóa người dùng')
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
/* Page Layout */
.admin-users {
  width: 100%;
  animation: fadeInUp 0.6s ease-out;
}

/* Header Section */
.page-header {
  margin-bottom: 32px;
  padding: 32px;
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 184, 148, 0.05) 100%);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 170, 0.2);
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.title-icon {
  color: #00d4aa;
  font-size: 32px;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #00d4aa;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* Toolbar */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: rgba(24, 24, 28, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.toolbar-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-container {
  min-width: 320px;
}

.search-input {
  border-radius: 12px;
}

.filter-container {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.refresh-btn, .add-btn {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refresh-btn:hover, .add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.add-btn:hover {
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

/* Table Container */
.table-container {
  background: rgba(24, 24, 28, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.table-container:hover {
  border-color: rgba(0, 212, 170, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Modern Table Styles */
:deep(.modern-users-table .n-data-table-th) {
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 700;
  color: #ffffff;
  font-size: 14px;
  padding: 20px 16px;
}

:deep(.modern-users-table .n-data-table-td) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding: 20px 16px;
  background: transparent;
}

:deep(.modern-users-table .n-data-table-tr:hover .n-data-table-td) {
  background: rgba(0, 212, 170, 0.05);
}

:deep(.modern-users-table .admin-row .n-data-table-td) {
  background: rgba(0, 212, 170, 0.03);
}

:deep(.modern-users-table .disabled-row .n-data-table-td) {
  background: rgba(255, 107, 107, 0.03);
  opacity: 0.7;
}

/* User Cell Styles */
.user-cell {
  display: flex;
  align-items: center;
}

.user-info {
  flex: 1;
}

.user-name {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.username {
  font-weight: 600;
  color: #ffffff;
  font-size: 16px;
}

.user-email {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 4px;
}

.user-meta {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.join-date, .last-seen {
  font-weight: 500;
}

/* Status Cell */
.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.status-cell.active {
  color: #00d4aa;
}

.status-cell.inactive {
  color: #ff6b6b;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background: #00d4aa;
}

.status-indicator.inactive {
  background: #ff6b6b;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

/* Modal Styles */
.custom-modal {
  backdrop-filter: blur(20px);
}

.modal-card {
  background: rgba(24, 24, 28, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

:deep(.modal-card .n-card-header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px 24px 16px 24px;
}

:deep(.modal-card .n-card-header .n-card-header__main) {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

:deep(.modal-card .n-card__content) {
  padding: 24px;
}

:deep(.modal-card .n-card__footer) {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
}

/* Form Styles */
.user-form {
  margin-bottom: 16px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

:deep(.user-form .n-form-item-label) {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  margin-bottom: 8px;
}

:deep(.user-form .n-input) {
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.user-form .n-input:hover) {
  border-color: rgba(0, 212, 170, 0.3);
}

:deep(.user-form .n-input:focus-within) {
  border-color: #00d4aa;
  box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

:deep(.user-form .n-select) {
  border-radius: 12px;
}

:deep(.user-form .n-base-selection) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

:deep(.user-form .n-base-selection:hover) {
  border-color: rgba(0, 212, 170, 0.3);
}

:deep(.user-form .n-base-selection.n-base-selection--focused) {
  border-color: #00d4aa;
  box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-actions .n-button {
  border-radius: 12px;
  font-weight: 500;
  padding: 0 24px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left {
    width: 100%;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 20px;
  }

  .toolbar {
    padding: 16px;
  }

  .search-container {
    min-width: auto;
    width: 100%;
  }

  .filter-container {
    width: 100%;
    justify-content: center;
  }

  .header-stats {
    gap: 16px;
  }

  .stat-value {
    font-size: 24px;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
