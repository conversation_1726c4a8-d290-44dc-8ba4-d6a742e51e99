<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $user = requireAdmin();

    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $limit = min($limit, 100); // Giới hạn tối đa 100 records

    // Lấy danh sách hoạt động gần đây
    $stmt = $pdo->prepare("
        SELECT 
            aa.id, aa.admin_id, aa.action, aa.target_type, aa.target_id,
            aa.description, aa.ip, aa.created_at,
            u.username as admin_username,
            u.nickname as admin_nickname,
            u.avatar as admin_avatar
        FROM admin_activities aa
        LEFT JOIN users u ON aa.admin_id = u.id
        ORDER BY aa.created_at DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $activities = $stmt->fetchAll();

    // Format dữ liệu
    $formattedActivities = array_map(function ($activity) {
        return [
            'id' => (int)$activity['id'],
            'admin_id' => (int)$activity['admin_id'],
            'admin_username' => $activity['admin_username'],
            'admin_nickname' => $activity['admin_nickname'] ?: $activity['admin_username'],
            'admin_avatar' => $activity['admin_avatar'],
            'action' => $activity['action'],
            'target_type' => $activity['target_type'],
            'target_id' => $activity['target_id'] ? (int)$activity['target_id'] : null,
            'description' => $activity['description'],
            'ip' => $activity['ip'],
            'created_at' => $activity['created_at']
        ];
    }, $activities);

    // Ghi log hoạt động admin
    logAdminActivity($user['id'], 'view_activities', 'Xem danh sách hoạt động');

    jsonResponse(['success' => true, 'message' => 'Lấy danh sách hoạt động thành công', 'activities' => $formattedActivities]);

} catch (Exception $e) {
    error_log("Activities API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

// Hàm logAdminActivity nên được chuyển vào file config.php hoặc file chung để tránh lặp lại.
// Tuy nhiên, theo yêu cầu, tôi sẽ định nghĩa nó ở đây để file có thể hoạt động độc lập.
function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'];
    $stmt = $pdo->prepare(
        "INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) 
         VALUES (?, ?, ?, ?, ?, ?)"
    );
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $ip]);
}
