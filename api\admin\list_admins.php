<?php
require_once '../config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $user = requireAuth();

    // Lấy danh sách admin và trạng thái online
    $stmt = $pdo->prepare("
        SELECT 
            id, username, nickname, avatar, last_seen,
            (last_seen >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)) as is_online
        FROM users 
        WHERE is_admin = 1 AND status = 1
        ORDER BY is_online DESC, last_seen DESC
    ");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    // Format dữ liệu
    $formattedAdmins = array_map(fn($admin) => [
        'id' => (int)$admin['id'],
        'username' => $admin['username'],
        'nickname' => $admin['nickname'] ?: $admin['username'],
        'avatar' => $admin['avatar'],
        'last_seen' => $admin['last_seen'],
        'is_online' => (int)$admin['is_online']
    ], $admins);
    
    // Nếu user là admin, lấy thêm thống kê tin nhắn chưa đọc cho chính mình
    if ($user['is_admin']) {
        // Tìm admin hiện tại trong danh sách đã format
        $currentAdminKey = array_search($user['id'], array_column($formattedAdmins, 'id'));

        if ($currentAdminKey !== false) {
            // Đếm tin nhắn chưa đọc
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM admin_messages 
                WHERE admin_id = ? AND is_read = 0 AND sender_id != ?
            ");
            $stmt->execute([$user['id'], $user['id']]);
            $formattedAdmins[$currentAdminKey]['unread_count'] = (int)$stmt->fetchColumn();
        }
    }
    
    jsonResponse([
        'success' => true,
        'message' => 'Lấy danh sách admin thành công',
        'admins' => $formattedAdmins
    ]);

} catch (Exception $e) {
    error_log("List Admins API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}
