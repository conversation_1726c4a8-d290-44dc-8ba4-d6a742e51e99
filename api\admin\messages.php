<?php
require_once '../config.php';

try {
    $user = requireAdmin();

    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetMessages($user);
            break;
        case 'DELETE':
            handleDeleteMessage($user);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Messages API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

function handleGetMessages($admin) {
    global $pdo;
    
    $search = trim($_GET['search'] ?? '');
    $userId = (int)($_GET['user_id'] ?? 0);
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(1, (int)($_GET['limit'] ?? 50)));
    $offset = ($page - 1) * $limit;
    
    $whereClauses = [];
    $params = [];
    
    if ($search) {
        $whereClauses[] = "(m.content LIKE ? OR u.username LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($userId > 0) {
        $whereClauses[] = "m.user_id = ?";
        $params[] = $userId;
    }

    $whereSql = count($whereClauses) > 0 ? 'WHERE ' . implode(' AND ', $whereClauses) : '';
    
    $countStmt = $pdo->prepare("SELECT COUNT(m.id) FROM messages m LEFT JOIN users u ON m.user_id = u.id $whereSql");
    $countStmt->execute($params);
    $total = (int)$countStmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT m.id, m.user_id, m.content, m.img, m.ip, m.location, m.created_at,
               u.username, u.nickname, u.avatar, u.is_admin as user_is_admin
        FROM messages m
        LEFT JOIN users u ON m.user_id = u.id
        $whereSql
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute(array_merge($params, [$limit, $offset]));
    $messages = $stmt->fetchAll();
    
    $formattedMessages = array_map(fn($msg) => [
        'id' => (int)$msg['id'],
        'user_id' => (int)$msg['user_id'],
        'username' => $msg['username'],
        'nickname' => $msg['nickname'] ?: $msg['username'],
        'avatar' => $msg['avatar'],
        'user_is_admin' => (int)$msg['user_is_admin'],
        'content' => $msg['content'],
        'img' => $msg['img'],
        'ip' => $msg['ip'],
        'location' => $msg['location'],
        'created_at' => $msg['created_at']
    ], $messages);
    
    logAdminActivity($admin['id'], 'view_messages', 'Xem danh sách tin nhắn');
    
    jsonResponse([
        'success' => true,
        'messages' => $formattedMessages,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleDeleteMessage($admin) {
    global $pdo;
    
    $messageId = (int)($_GET['id'] ?? 0);
    if (!$messageId) {
        jsonResponse(['success' => false, 'message' => 'ID tin nhắn không hợp lệ'], 400);
    }
    
    $stmt = $pdo->prepare("SELECT m.content, u.username FROM messages m LEFT JOIN users u ON m.user_id = u.id WHERE m.id = ?");
    $stmt->execute([$messageId]);
    $message = $stmt->fetch();
    
    if (!$message) {
        jsonResponse(['success' => false, 'message' => 'Tin nhắn không tồn tại'], 404);
    }
    
    $stmt = $pdo->prepare("DELETE FROM messages WHERE id = ?");
    $stmt->execute([$messageId]);
    
    $description = "Xóa tin nhắn của {$message['username']}: " . mb_substr($message['content'], 0, 50);
    logAdminActivity($admin['id'], 'delete_message', $description, 'message', $messageId);
    
    jsonResponse(['success' => true, 'message' => 'Xóa tin nhắn thành công']);
}

function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'];
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $ip]);
}
