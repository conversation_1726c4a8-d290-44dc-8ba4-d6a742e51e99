<?php
require_once '../config.php';

try {
    $user = requireAdmin();
    
    // Ghi log hoạt động admin
    logAdminActivity($user['id'], 'test_api', 'Thực hiện test API thành công');

    jsonResponse([
        'success' => true,
        'message' => 'Test API thành công',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'is_admin' => $user['is_admin'],
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Test API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    jsonResponse(['success' => false, 'message' => 'Lỗi server: ' . $e->getMessage()], 500);
}

function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'];
    $stmt = $pdo->prepare(
        "INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) 
         VALUES (?, ?, ?, ?, ?, ?)"
    );
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $ip]);
}
