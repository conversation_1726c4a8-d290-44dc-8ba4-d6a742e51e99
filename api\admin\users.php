<?php
require_once '../config.php';

try {
    $user = requireAdmin();

    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetUsers($user);
            break;
        case 'POST':
            handleCreateUser($user);
            break;
        case 'PUT':
            handleUpdateUser($user);
            break;
        case 'DELETE':
            handleDeleteUser($user);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Users API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

function handleGetUsers($admin) {
    global $pdo;
    
    $search = trim($_GET['search'] ?? '');
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $whereClause = '';
    $params = [];
    
    if ($search) {
        $whereClause = "WHERE username LIKE :search OR email LIKE :search OR nickname LIKE :search";
        $params[':search'] = "%$search%";
    }
    
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM users $whereClause");
    $countStmt->execute($params);
    $total = (int)$countStmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT id, username, email, nickname, signature, avatar,
               is_admin, status, ip, location, last_login,
               last_seen, created_at
        FROM users
        $whereClause
        ORDER BY created_at DESC
        LIMIT :limit OFFSET :offset
    ");
    
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    if ($search) {
        $stmt->bindValue(':search', $params[':search']);
    }
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    $formattedUsers = array_map(fn($user) => [
        'id' => (int)$user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'nickname' => $user['nickname'],
        'signature' => $user['signature'],
        'avatar' => $user['avatar'],
        'is_admin' => (int)$user['is_admin'],
        'status' => (int)$user['status'],
        'ip' => $user['ip'],
        'location' => $user['location'],
        'last_login' => $user['last_login'],
        'last_seen' => $user['last_seen'],
        'created_at' => $user['created_at']
    ], $users);
    
    logAdminActivity($admin['id'], 'view_users', 'Xem danh sách người dùng');
    
    jsonResponse([
        'success' => true,
        'users' => $formattedUsers,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleCreateUser($admin) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $username = trim($input['username'] ?? '');
    $email = filter_var(trim($input['email'] ?? ''), FILTER_VALIDATE_EMAIL);
    $password = $input['password'] ?? '';
    
    if (empty($username) || empty($email) || empty($password)) {
        jsonResponse(['success' => false, 'message' => 'Thiếu thông tin bắt buộc (username, email, password)'], 400);
    }
    if (strlen($username) < 3 || strlen($username) > 50) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập từ 3-50 ký tự'], 400);
    }
    if (!$email) {
        jsonResponse(['success' => false, 'message' => 'Email không hợp lệ'], 400);
    }
    if (strlen($password) < 6) {
        jsonResponse(['success' => false, 'message' => 'Mật khẩu tối thiểu 6 ký tự'], 400);
    }
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);
    if ($stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập hoặc email đã tồn tại'], 400);
    }
    
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $pdo->prepare(
        "INSERT INTO users (username, email, password, nickname, signature, is_admin, ip) 
         VALUES (?, ?, ?, ?, ?, ?, ?)"
    );
    $stmt->execute([
        $username, $email, $hashedPassword, 
        trim($input['nickname'] ?? ''), 
        trim($input['signature'] ?? ''), 
        (int)($input['is_admin'] ?? 0), 
        $_SERVER['REMOTE_ADDR']
    ]);
    
    $userId = (int)$pdo->lastInsertId();
    logAdminActivity($admin['id'], 'create_user', "Tạo người dùng mới: $username", 'user', $userId);
    
    jsonResponse(['success' => true, 'message' => 'Tạo người dùng thành công', 'user_id' => $userId]);
}

function handleUpdateUser($admin) {
    global $pdo;
    
    $userId = (int)($_GET['id'] ?? 0);
    if (!$userId) {
        jsonResponse(['success' => false, 'message' => 'ID người dùng không hợp lệ'], 400);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $username = trim($input['username'] ?? '');
    $email = filter_var(trim($input['email'] ?? ''), FILTER_VALIDATE_EMAIL);
    
    if (empty($username) || empty($email)) {
        jsonResponse(['success' => false, 'message' => 'Username và email là bắt buộc'], 400);
    }
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
    $stmt->execute([$username, $email, $userId]);
    if ($stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập hoặc email đã được sử dụng'], 400);
    }
    
    $stmt = $pdo->prepare(
        "UPDATE users SET username = ?, email = ?, nickname = ?, signature = ?, is_admin = ?, status = ? WHERE id = ?"
    );
    $stmt->execute([
        $username, 
        $email, 
        trim($input['nickname'] ?? ''), 
        trim($input['signature'] ?? ''), 
        (int)($input['is_admin'] ?? 0), 
        (int)($input['status'] ?? 1), 
        $userId
    ]);
    
    logAdminActivity($admin['id'], 'update_user', "Cập nhật người dùng: $username", 'user', $userId);
    
    jsonResponse(['success' => true, 'message' => 'Cập nhật người dùng thành công']);
}

function handleDeleteUser($admin) {
    global $pdo;
    
    $userId = (int)($_GET['id'] ?? 0);
    if (!$userId) {
        jsonResponse(['success' => false, 'message' => 'ID người dùng không hợp lệ'], 400);
    }
    
    if ($userId === $admin['id']) {
        jsonResponse(['success' => false, 'message' => 'Không thể xóa chính mình'], 400);
    }
    
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    if (!$user) {
        jsonResponse(['success' => false, 'message' => 'Người dùng không tồn tại'], 404);
    }
    
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    
    logAdminActivity($admin['id'], 'delete_user', "Xóa người dùng: {$user['username']}", 'user', $userId);
    
    jsonResponse(['success' => true, 'message' => 'Xóa người dùng thành công']);
}

function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $ip = $_SERVER['REMOTE_ADDR'];
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $ip]);
}
