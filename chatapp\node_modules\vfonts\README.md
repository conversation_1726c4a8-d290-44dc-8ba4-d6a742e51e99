# vfonts
Integreted fonts for easy usage.

## Usage
```js
// in your js
import 'vfonts/{font-name}.css'
// for available fonts, see the following section
```
```css
/** in your css */
selector {
  font-family: v-sans, v-mono, other-fallbacks;
}

/** for available font weights, see the following section */
selector {
  font-weight: 400; /** regular */
}

selector {
  font-weight: 500; /** medium */
}
```

## Available Fonts
### `v-sans`
- `FiraSans.css`
  - font weight `400`, `500`
- `IBMPlexSans.css`
  - font weight `400`, `500`
- `Inter.css`
  - font weight `400`, `500`
- `Lato.css`
  - font weight `400`, `600`
- `OpenSans.css`
  - font weight `400`, `600`
- `Roboto.css`
  - font weight `400`, `500`
- `RobotoSlab.css`
  - font weight `400`, `500`
### `v-mono`
- `FiraCode.css`
  - font weight `400`