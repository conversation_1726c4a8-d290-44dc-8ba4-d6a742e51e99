{"name": "chatapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@icon-park/vue-next": "^1.4.2", "@vee-validate/rules": "^4.15.1", "axios": "^1.10.0", "filepond": "^4.32.8", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-crop": "^2.0.6", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "filepond-plugin-image-resize": "^2.0.10", "filepond-plugin-image-transform": "^3.8.7", "filepond-plugin-image-validate-size": "^1.2.7", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-filepond": "^7.0.4", "vue-image-zoomer": "^2.4.4", "vue-router": "^4.5.1", "vue3-emoji-picker": "^1.1.8", "yup": "^1.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "vfonts": "^0.0.3", "vite": "^7.0.4"}}